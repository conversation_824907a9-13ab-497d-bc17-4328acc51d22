version: '3.8'

# 本地开发环境覆盖配置
services:
  # MySQL开发配置
  mysql:
    ports:
      - "3306:3306"
    volumes:
      - ../scripts/database:/docker-entrypoint-initdb.d:ro
      - ./mysql/conf.d:/etc/mysql/conf.d:ro
    environment:
      MYSQL_ROOT_PASSWORD: root123456
    command: --default-authentication-plugin=mysql_native_password --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci

  # Redis开发配置
  redis:
    ports:
      - "6379:6379"
    volumes:
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf

  # Nacos开发配置
  nacos:
    ports:
      - "8848:8848"
      - "9848:9848"
    environment:
      MODE: standalone
      PREFER_HOST_MODE: hostname
      JVM_XMS: 512m
      JVM_XMX: 512m
      JVM_XMN: 256m
    volumes:
      - ./nacos/logs:/home/<USER>/logs
      - ./nacos/data:/home/<USER>/data

  # 认证服务开发配置
  auth-service:
    environment:
      JAVA_OPTS: -Xms512m -Xmx1024m -XX:MetaspaceSize=128m -Dspring.profiles.active=dev -Ddebug=true
      SPRING_DEVTOOLS_RESTART_ENABLED: true
      LOGGING_LEVEL_COM_MICROSYSTEM: DEBUG
    volumes:
      - ../auth-service/src/main/resources:/app/config:ro
      - ./logs/auth-service:/app/logs
    ports:
      - "8081:8081"
      - "5005:5005"  # Debug端口
    command: >
      sh -c "
        java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005
        $$JAVA_OPTS -jar app.jar
      "

  # 业务服务开发配置
  business-service:
    environment:
      JAVA_OPTS: -Xms1024m -Xmx2048m -XX:MetaspaceSize=256m -Dspring.profiles.active=dev -Ddebug=true
      SPRING_DEVTOOLS_RESTART_ENABLED: true
      LOGGING_LEVEL_COM_MICROSYSTEM: DEBUG
    volumes:
      - ../business-service/src/main/resources:/app/config:ro
      - ./logs/business-service:/app/logs
    ports:
      - "8082:8082"
      - "5006:5006"  # Debug端口
    command: >
      sh -c "
        java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5006
        $$JAVA_OPTS -jar app.jar
      "

  # 任务服务开发配置
  job-service:
    environment:
      JAVA_OPTS: -Xms512m -Xmx1024m -XX:MetaspaceSize=128m -Dspring.profiles.active=dev -Ddebug=true
      SPRING_DEVTOOLS_RESTART_ENABLED: true
      LOGGING_LEVEL_COM_MICROSYSTEM: DEBUG
    volumes:
      - ../job-service/src/main/resources:/app/config:ro
      - ./logs/job-service:/app/logs
    ports:
      - "8083:8083"
      - "5007:5007"  # Debug端口
    command: >
      sh -c "
        java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5007
        $$JAVA_OPTS -jar app.jar
      "

  # 网关服务开发配置
  gateway-service:
    environment:
      JAVA_OPTS: -Xms512m -Xmx1024m -XX:MetaspaceSize=128m -Dspring.profiles.active=dev -Ddebug=true
      SPRING_DEVTOOLS_RESTART_ENABLED: true
      LOGGING_LEVEL_COM_MICROSYSTEM: DEBUG
    volumes:
      - ../gateway-service/src/main/resources:/app/config:ro
      - ./logs/gateway-service:/app/logs
    ports:
      - "8080:8080"
      - "5008:5008"  # Debug端口
    command: >
      sh -c "
        java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5008
        $$JAVA_OPTS -jar app.jar
      "

# 开发环境数据卷
volumes:
  mysql-dev-data:
  redis-dev-data:
  nacos-dev-data:
