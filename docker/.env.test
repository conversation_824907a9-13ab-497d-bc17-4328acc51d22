# 测试环境配置文件

# 环境标识
COMPOSE_PROJECT_NAME=microsystem-test
DEPLOY_ENV=test

# 镜像配置
DOCKER_REGISTRY=microsystem
IMAGE_TAG=1.0.0-test

# MySQL配置
MYSQL_ROOT_PASSWORD=test_root_password_2024
MYSQL_DATABASE=microsystem_auth
MYSQL_USER=microsystem
MYSQL_PASSWORD=test_microsystem_password_2024
MYSQL_PORT=3306

# Redis配置
REDIS_PASSWORD=test_redis_password_2024
REDIS_PORT=6379

# Nacos配置
NACOS_PORT=8848
NACOS_USERNAME=nacos
NACOS_PASSWORD=test_nacos_password_2024

# Sentinel配置
SENTINEL_PORT=8858
SENTINEL_USERNAME=sentinel
SENTINEL_PASSWORD=test_sentinel_password_2024

# Pulsar配置
PULSAR_BROKER_PORT=6650
PULSAR_WEB_PORT=8090

# PowerJob配置
POWERJOB_PORT=7700
POWERJOB_USERNAME=admin
POWERJOB_PASSWORD=test_powerjob_password_2024

# 微服务端口配置
GATEWAY_PORT=8080
AUTH_SERVICE_PORT=8081
BUSINESS_SERVICE_PORT=8082
JOB_SERVICE_PORT=8083

# JVM配置
GATEWAY_JAVA_OPTS=-Xms1024m -Xmx2048m -XX:MetaspaceSize=256m
AUTH_JAVA_OPTS=-Xms1024m -Xmx2048m -XX:MetaspaceSize=256m
BUSINESS_JAVA_OPTS=-Xms2048m -Xmx4096m -XX:MetaspaceSize=512m
JOB_JAVA_OPTS=-Xms1024m -Xmx2048m -XX:MetaspaceSize=256m

# 日志级别
LOG_LEVEL=INFO
ROOT_LOG_LEVEL=WARN

# 数据库连接配置
DB_MAX_CONNECTIONS=50
DB_MIN_IDLE=10
DB_CONNECTION_TIMEOUT=30000

# Redis连接配置
REDIS_MAX_CONNECTIONS=20
REDIS_MIN_IDLE=5
REDIS_TIMEOUT=10000

# 健康检查配置
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=15s
HEALTH_CHECK_RETRIES=5
HEALTH_CHECK_START_PERIOD=90s

# 测试模式配置
DEBUG_MODE=false
HOT_RELOAD=false
SWAGGER_ENABLED=true
