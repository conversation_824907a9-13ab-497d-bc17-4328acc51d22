# 开发环境配置文件

# 环境标识
COMPOSE_PROJECT_NAME=microsystem-dev
DEPLOY_ENV=dev

# 镜像配置
DOCKER_REGISTRY=microsystem
IMAGE_TAG=1.0.0-dev

# MySQL配置
MYSQL_ROOT_PASSWORD=root123456
MYSQL_DATABASE=microsystem_auth
MYSQL_USER=microsystem
MYSQL_PASSWORD=microsystem123
MYSQL_PORT=3306

# Redis配置
REDIS_PASSWORD=
REDIS_PORT=6379

# Nacos配置
NACOS_PORT=8848
NACOS_USERNAME=nacos
NACOS_PASSWORD=nacos

# Sentinel配置
SENTINEL_PORT=8858
SENTINEL_USERNAME=sentinel
SENTINEL_PASSWORD=sentinel

# Pulsar配置
PULSAR_BROKER_PORT=6650
PULSAR_WEB_PORT=8090

# PowerJob配置
POWERJOB_PORT=7700
POWERJOB_USERNAME=admin
POWERJOB_PASSWORD=123456

# 微服务端口配置
GATEWAY_PORT=8080
AUTH_SERVICE_PORT=8081
BUSINESS_SERVICE_PORT=8082
JOB_SERVICE_PORT=8083

# JVM配置
GATEWAY_JAVA_OPTS=-Xms512m -Xmx1024m -XX:MetaspaceSize=128m
AUTH_JAVA_OPTS=-Xms512m -Xmx1024m -XX:MetaspaceSize=128m
BUSINESS_JAVA_OPTS=-Xms1024m -Xmx2048m -XX:MetaspaceSize=256m
JOB_JAVA_OPTS=-Xms512m -Xmx1024m -XX:MetaspaceSize=128m

# 日志级别
LOG_LEVEL=DEBUG
ROOT_LOG_LEVEL=INFO

# 数据库连接配置
DB_MAX_CONNECTIONS=20
DB_MIN_IDLE=5
DB_CONNECTION_TIMEOUT=30000

# Redis连接配置
REDIS_MAX_CONNECTIONS=8
REDIS_MIN_IDLE=0
REDIS_TIMEOUT=10000

# 健康检查配置
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=10s
HEALTH_CHECK_RETRIES=3
HEALTH_CHECK_START_PERIOD=60s

# 开发模式配置
DEBUG_MODE=true
HOT_RELOAD=true
SWAGGER_ENABLED=true
