# 生产环境配置文件

# 环境标识
COMPOSE_PROJECT_NAME=microsystem-prod
DEPLOY_ENV=prod

# 镜像配置
DOCKER_REGISTRY=microsystem
IMAGE_TAG=1.0.0

# MySQL配置
MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
MYSQL_DATABASE=microsystem_auth
MYSQL_USER=microsystem
MYSQL_PASSWORD=${MYSQL_PASSWORD}
MYSQL_PORT=3306

# Redis配置
REDIS_PASSWORD=${REDIS_PASSWORD}
REDIS_PORT=6379

# Nacos配置
NACOS_PORT=8848
NACOS_USERNAME=${NACOS_USERNAME}
NACOS_PASSWORD=${NACOS_PASSWORD}

# Sentinel配置
SENTINEL_PORT=8858
SENTINEL_USERNAME=${SENTINEL_USERNAME}
SENTINEL_PASSWORD=${SENTINEL_PASSWORD}

# Pulsar配置
PULSAR_BROKER_PORT=6650
PULSAR_WEB_PORT=8090

# PowerJob配置
POWERJOB_PORT=7700
POWERJOB_USERNAME=${POWERJOB_USERNAME}
POWERJOB_PASSWORD=${POWERJOB_PASSWORD}

# 微服务端口配置
GATEWAY_PORT=8080
AUTH_SERVICE_PORT=8081
BUSINESS_SERVICE_PORT=8082
JOB_SERVICE_PORT=8083

# JVM配置
GATEWAY_JAVA_OPTS=-Xms2048m -Xmx4096m -XX:MetaspaceSize=512m -XX:+UseG1GC
AUTH_JAVA_OPTS=-Xms2048m -Xmx4096m -XX:MetaspaceSize=512m -XX:+UseG1GC
BUSINESS_JAVA_OPTS=-Xms4096m -Xmx8192m -XX:MetaspaceSize=1024m -XX:+UseG1GC
JOB_JAVA_OPTS=-Xms2048m -Xmx4096m -XX:MetaspaceSize=512m -XX:+UseG1GC

# 日志级别
LOG_LEVEL=WARN
ROOT_LOG_LEVEL=ERROR

# 数据库连接配置
DB_MAX_CONNECTIONS=100
DB_MIN_IDLE=20
DB_CONNECTION_TIMEOUT=30000

# Redis连接配置
REDIS_MAX_CONNECTIONS=50
REDIS_MIN_IDLE=10
REDIS_TIMEOUT=10000

# 健康检查配置
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=20s
HEALTH_CHECK_RETRIES=5
HEALTH_CHECK_START_PERIOD=120s

# 生产模式配置
DEBUG_MODE=false
HOT_RELOAD=false
SWAGGER_ENABLED=false

# 安全配置
ENABLE_SECURITY_HEADERS=true
ENABLE_RATE_LIMITING=true
ENABLE_REQUEST_LOGGING=false

# 监控配置
ENABLE_METRICS=true
ENABLE_TRACING=true
METRICS_EXPORT_INTERVAL=60s
