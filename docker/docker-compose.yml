version: '3.8'

# 网络配置
networks:
  microsystem-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  mysql-data:
  redis-data:
  nacos-data:
  pulsar-data:
  powerjob-data:

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: microsystem-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root123456
      MYSQL_DATABASE: microsystem_auth
      MYSQL_USER: microsystem
      MYSQL_PASSWORD: microsystem123
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - mysql-data:/var/lib/mysql
      - ../scripts/database:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      microsystem-network:
        ipv4_address: ***********
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: microsystem-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes
    networks:
      microsystem-network:
        ipv4_address: ***********
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 10s
      retries: 5

  # Nacos注册中心
  nacos:
    image: nacos/nacos-server:v2.3.0
    container_name: microsystem-nacos
    restart: always
    environment:
      MODE: standalone
      SPRING_DATASOURCE_PLATFORM: mysql
      MYSQL_SERVICE_HOST: mysql
      MYSQL_SERVICE_PORT: 3306
      MYSQL_SERVICE_DB_NAME: nacos
      MYSQL_SERVICE_USER: microsystem
      MYSQL_SERVICE_PASSWORD: microsystem123
      MYSQL_SERVICE_DB_PARAM: characterEncoding=utf8&connectTimeout=1000&socketTimeout=3000&autoReconnect=true&useUnicode=true&useSSL=false&serverTimezone=Asia/Shanghai
      TZ: Asia/Shanghai
    ports:
      - "8848:8848"
      - "9848:9848"
    volumes:
      - nacos-data:/home/<USER>/data
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      microsystem-network:
        ipv4_address: ***********
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8848/nacos/v1/ns/operator/metrics"]
      timeout: 10s
      retries: 10

  # Sentinel控制台
  sentinel:
    image: bladex/sentinel-dashboard:1.8.6
    container_name: microsystem-sentinel
    restart: always
    environment:
      TZ: Asia/Shanghai
    ports:
      - "8858:8858"
    networks:
      microsystem-network:
        ipv4_address: ***********

  # Pulsar消息队列
  pulsar:
    image: apachepulsar/pulsar:3.1.1
    container_name: microsystem-pulsar
    restart: always
    environment:
      TZ: Asia/Shanghai
    ports:
      - "6650:6650"
      - "8090:8080"
    volumes:
      - pulsar-data:/pulsar/data
    command: bin/pulsar standalone
    networks:
      microsystem-network:
        ipv4_address: ***********
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/admin/v2/clusters"]
      timeout: 10s
      retries: 10

  # PowerJob任务调度
  powerjob:
    image: tjqq/powerjob-server:4.3.6
    container_name: microsystem-powerjob
    restart: always
    environment:
      PARAMS: --spring.profiles.active=product --spring.datasource.core.jdbc-url=***************************************************************************************************** --spring.datasource.core.username=microsystem --spring.datasource.core.password=microsystem123
      TZ: Asia/Shanghai
    ports:
      - "7700:7700"
    volumes:
      - powerjob-data:/root/powerjob-server
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      microsystem-network:
        ipv4_address: ***********

  # 认证服务
  auth-service:
    build:
      context: ../auth-service
      dockerfile: Dockerfile
    image: microsystem/auth-service:1.0.0
    container_name: microsystem-auth-service
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: ************************************************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: microsystem
      SPRING_DATASOURCE_PASSWORD: microsystem123
      SPRING_DATA_REDIS_HOST: redis
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR: nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR: nacos:8848
      TZ: Asia/Shanghai
    ports:
      - "8081:8081"
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      nacos:
        condition: service_healthy
    networks:
      microsystem-network:
        ipv4_address: ***********

  # 业务服务
  business-service:
    build:
      context: ../business-service
      dockerfile: Dockerfile
    image: microsystem/business-service:1.0.0
    container_name: microsystem-business-service
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: ****************************************************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: microsystem
      SPRING_DATASOURCE_PASSWORD: microsystem123
      SPRING_DATA_REDIS_HOST: redis
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR: nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR: nacos:8848
      PULSAR_SERVICE_URL: pulsar://pulsar:6650
      TZ: Asia/Shanghai
    ports:
      - "8082:8082"
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      nacos:
        condition: service_healthy
      pulsar:
        condition: service_healthy
    networks:
      microsystem-network:
        ipv4_address: ***********

  # 任务服务
  job-service:
    build:
      context: ../job-service
      dockerfile: Dockerfile
    image: microsystem/job-service:1.0.0
    container_name: microsystem-job-service
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: ***********************************************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: microsystem
      SPRING_DATASOURCE_PASSWORD: microsystem123
      SPRING_DATA_REDIS_HOST: redis
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR: nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR: nacos:8848
      POWERJOB_WORKER_SERVER_ADDRESS: powerjob:7700
      PULSAR_SERVICE_URL: pulsar://pulsar:6650
      TZ: Asia/Shanghai
    ports:
      - "8083:8083"
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      nacos:
        condition: service_healthy
      powerjob:
        condition: service_started
      pulsar:
        condition: service_healthy
    networks:
      microsystem-network:
        ipv4_address: ***********

  # 网关服务
  gateway-service:
    build:
      context: ../gateway-service
      dockerfile: Dockerfile
    image: microsystem/gateway-service:1.0.0
    container_name: microsystem-gateway-service
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATA_REDIS_HOST: redis
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR: nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR: nacos:8848
      TZ: Asia/Shanghai
    ports:
      - "8080:8080"
    depends_on:
      redis:
        condition: service_healthy
      nacos:
        condition: service_healthy
      auth-service:
        condition: service_healthy
      business-service:
        condition: service_healthy
      job-service:
        condition: service_healthy
    networks:
      microsystem-network:
        ipv4_address: ***********
