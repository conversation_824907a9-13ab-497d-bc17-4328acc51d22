# 基础镜像Dockerfile
FROM openjdk:17-jdk-slim

# 维护者信息
LABEL maintainer="MicroSystem Team <<EMAIL>>"
LABEL version="1.0.0"
LABEL description="MicroSystem Base Image"

# 设置环境变量
ENV LANG=C.UTF-8
ENV TZ=Asia/Shanghai
ENV JAVA_OPTS="-Xms512m -Xmx1024m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=256m"
ENV SPRING_PROFILES_ACTIVE=docker

# 安装必要的工具
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        curl \
        wget \
        telnet \
        netcat \
        procps \
        tzdata && \
    ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && \
    echo $TZ > /etc/timezone && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# 创建应用目录
RUN mkdir -p /app/logs /app/config /app/data

# 设置工作目录
WORKDIR /app

# 创建非root用户
RUN groupadd -r microsystem && useradd -r -g microsystem microsystem
RUN chown -R microsystem:microsystem /app

# 切换到非root用户
USER microsystem

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health || exit 1

# 暴露端口
EXPOSE 8080

# 启动脚本
COPY docker-entrypoint.sh /app/
ENTRYPOINT ["/app/docker-entrypoint.sh"]
