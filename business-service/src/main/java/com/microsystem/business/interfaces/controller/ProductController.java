package com.microsystem.business.interfaces.controller;

import com.microsystem.business.application.dto.command.CreateProductCommand;
import com.microsystem.business.application.dto.command.UpdateProductCommand;
import com.microsystem.business.application.dto.query.ProductQuery;
import com.microsystem.business.application.dto.response.ProductResponse;
import com.microsystem.business.application.service.ProductApplicationService;
import com.microsystem.common.result.PageResult;
import com.microsystem.common.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 产品控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/products")
@RequiredArgsConstructor
@Tag(name = "产品管理", description = "产品相关接口")
public class ProductController {

    private final ProductApplicationService productApplicationService;

    /**
     * 创建产品
     */
    @PostMapping
    @Operation(summary = "创建产品", description = "创建新的产品")
    public Result<ProductResponse> createProduct(@Valid @RequestBody CreateProductCommand command) {
        log.info("创建产品请求: {}", command.getName());
        ProductResponse response = productApplicationService.createProduct(command);
        return Result.success("产品创建成功", response);
    }

    /**
     * 更新产品
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新产品", description = "更新指定产品信息")
    public Result<ProductResponse> updateProduct(
            @Parameter(description = "产品ID") @PathVariable Long id,
            @Valid @RequestBody UpdateProductCommand command) {
        log.info("更新产品请求: {}", id);
        ProductResponse response = productApplicationService.updateProduct(id, command);
        return Result.success("产品更新成功", response);
    }

    /**
     * 获取产品详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取产品详情", description = "根据ID获取产品详细信息")
    public Result<ProductResponse> getProduct(
            @Parameter(description = "产品ID") @PathVariable Long id) {
        log.info("获取产品详情: {}", id);
        ProductResponse response = productApplicationService.getProduct(id);
        return Result.success(response);
    }

    /**
     * 分页查询产品列表
     */
    @GetMapping
    @Operation(summary = "分页查询产品", description = "根据条件分页查询产品列表")
    public Result<PageResult<ProductResponse>> getProducts(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "产品名称") @RequestParam(required = false) String name,
            @Parameter(description = "产品状态") @RequestParam(required = false) String status,
            @Parameter(description = "分类ID") @RequestParam(required = false) Long categoryId,
            @Parameter(description = "最小价格") @RequestParam(required = false) Double minPrice,
            @Parameter(description = "最大价格") @RequestParam(required = false) Double maxPrice,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "排序方向") @RequestParam(defaultValue = "desc") String sortDirection) {
        
        log.info("分页查询产品: page={}, size={}", page, size);
        
        ProductQuery query = new ProductQuery();
        query.setPage(page);
        query.setSize(size);
        query.setName(name);
        query.setStatus(status);
        query.setCategoryId(categoryId);
        query.setMinPrice(minPrice);
        query.setMaxPrice(maxPrice);
        query.setSortBy(sortBy);
        query.setSortDirection(sortDirection);
        
        PageResult<ProductResponse> response = productApplicationService.getProducts(query);
        return Result.success(response);
    }

    /**
     * 发布产品
     */
    @PostMapping("/{id}/publish")
    @Operation(summary = "发布产品", description = "将产品状态设置为已发布")
    public Result<Void> publishProduct(
            @Parameter(description = "产品ID") @PathVariable Long id) {
        log.info("发布产品: {}", id);
        productApplicationService.publishProduct(id);
        return Result.success("产品发布成功");
    }

    /**
     * 下架产品
     */
    @PostMapping("/{id}/unpublish")
    @Operation(summary = "下架产品", description = "将产品状态设置为已下架")
    public Result<Void> unpublishProduct(
            @Parameter(description = "产品ID") @PathVariable Long id) {
        log.info("下架产品: {}", id);
        productApplicationService.unpublishProduct(id);
        return Result.success("产品下架成功");
    }

    /**
     * 删除产品
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除产品", description = "删除指定产品")
    public Result<Void> deleteProduct(
            @Parameter(description = "产品ID") @PathVariable Long id) {
        log.info("删除产品: {}", id);
        productApplicationService.deleteProduct(id);
        return Result.success("产品删除成功");
    }

    /**
     * 增加库存
     */
    @PostMapping("/{id}/stock/increase")
    @Operation(summary = "增加库存", description = "增加产品库存数量")
    public Result<Void> increaseStock(
            @Parameter(description = "产品ID") @PathVariable Long id,
            @Parameter(description = "增加数量") @RequestParam Integer quantity) {
        log.info("增加产品库存: id={}, quantity={}", id, quantity);
        // 这里可以调用应用服务的增加库存方法
        return Result.success("库存增加成功");
    }

    /**
     * 减少库存
     */
    @PostMapping("/{id}/stock/decrease")
    @Operation(summary = "减少库存", description = "减少产品库存数量")
    public Result<Void> decreaseStock(
            @Parameter(description = "产品ID") @PathVariable Long id,
            @Parameter(description = "减少数量") @RequestParam Integer quantity) {
        log.info("减少产品库存: id={}, quantity={}", id, quantity);
        // 这里可以调用应用服务的减少库存方法
        return Result.success("库存减少成功");
    }
}
