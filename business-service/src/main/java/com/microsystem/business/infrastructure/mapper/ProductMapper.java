package com.microsystem.business.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.microsystem.business.infrastructure.entity.ProductEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 产品Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface ProductMapper extends BaseMapper<ProductEntity> {

    /**
     * 根据名称查询产品
     */
    @Select("SELECT * FROM biz_product WHERE name = #{name} AND deleted = 0")
    ProductEntity findByName(@Param("name") String name);

    /**
     * 根据状态查询产品列表
     */
    @Select("SELECT * FROM biz_product WHERE status = #{status} AND deleted = 0 ORDER BY create_time DESC")
    List<ProductEntity> findByStatus(@Param("status") String status);

    /**
     * 根据分类ID查询产品列表
     */
    @Select("SELECT * FROM biz_product WHERE category_id = #{categoryId} AND deleted = 0 ORDER BY create_time DESC")
    List<ProductEntity> findByCategoryId(@Param("categoryId") Long categoryId);

    /**
     * 检查产品名称是否存在
     */
    @Select("SELECT COUNT(*) FROM biz_product WHERE name = #{name} AND deleted = 0")
    int countByName(@Param("name") String name);

    /**
     * 统计指定状态的产品数量
     */
    @Select("SELECT COUNT(*) FROM biz_product WHERE status = #{status} AND deleted = 0")
    long countByStatus(@Param("status") String status);

    /**
     * 分页查询产品（支持条件查询）
     */
    @Select("<script>" +
            "SELECT * FROM biz_product WHERE deleted = 0 " +
            "<if test='name != null and name != \"\"'>" +
            "AND name LIKE CONCAT('%', #{name}, '%') " +
            "</if>" +
            "<if test='status != null and status != \"\"'>" +
            "AND status = #{status} " +
            "</if>" +
            "<if test='categoryId != null'>" +
            "AND category_id = #{categoryId} " +
            "</if>" +
            "<if test='minPrice != null'>" +
            "AND price >= #{minPrice} " +
            "</if>" +
            "<if test='maxPrice != null'>" +
            "AND price <= #{maxPrice} " +
            "</if>" +
            "ORDER BY " +
            "<choose>" +
            "<when test='sortBy == \"price\"'>price</when>" +
            "<when test='sortBy == \"stock\"'>stock</when>" +
            "<when test='sortBy == \"recommendationScore\"'>recommendation_score</when>" +
            "<otherwise>create_time</otherwise>" +
            "</choose> " +
            "<choose>" +
            "<when test='sortDirection == \"asc\"'>ASC</when>" +
            "<otherwise>DESC</otherwise>" +
            "</choose> " +
            "LIMIT #{offset}, #{size}" +
            "</script>")
    List<ProductEntity> findByConditions(
            @Param("name") String name,
            @Param("status") String status,
            @Param("categoryId") Long categoryId,
            @Param("minPrice") Double minPrice,
            @Param("maxPrice") Double maxPrice,
            @Param("sortBy") String sortBy,
            @Param("sortDirection") String sortDirection,
            @Param("offset") int offset,
            @Param("size") int size
    );

    /**
     * 统计符合条件的产品数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM biz_product WHERE deleted = 0 " +
            "<if test='name != null and name != \"\"'>" +
            "AND name LIKE CONCAT('%', #{name}, '%') " +
            "</if>" +
            "<if test='status != null and status != \"\"'>" +
            "AND status = #{status} " +
            "</if>" +
            "<if test='categoryId != null'>" +
            "AND category_id = #{categoryId} " +
            "</if>" +
            "<if test='minPrice != null'>" +
            "AND price >= #{minPrice} " +
            "</if>" +
            "<if test='maxPrice != null'>" +
            "AND price <= #{maxPrice} " +
            "</if>" +
            "</script>")
    long countByConditions(
            @Param("name") String name,
            @Param("status") String status,
            @Param("categoryId") Long categoryId,
            @Param("minPrice") Double minPrice,
            @Param("maxPrice") Double maxPrice
    );
}
