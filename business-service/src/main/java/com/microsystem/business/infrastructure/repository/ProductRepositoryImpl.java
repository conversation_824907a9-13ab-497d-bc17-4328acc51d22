package com.microsystem.business.infrastructure.repository;

import com.microsystem.business.domain.model.product.*;
import com.microsystem.business.domain.model.shared.Money;
import com.microsystem.business.domain.repository.ProductRepository;
import com.microsystem.business.infrastructure.entity.ProductEntity;
import com.microsystem.business.infrastructure.mapper.ProductMapper;
import com.microsystem.common.util.JsonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Currency;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 产品仓储实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class ProductRepositoryImpl implements ProductRepository {

    private final ProductMapper productMapper;

    @Override
    public Product save(Product product) {
        ProductEntity entity = convertToEntity(product);
        
        if (entity.getId() == null) {
            // 新增
            productMapper.insert(entity);
            log.debug("新增产品: {}", entity.getId());
        } else {
            // 更新
            productMapper.updateById(entity);
            log.debug("更新产品: {}", entity.getId());
        }
        
        return convertToDomain(entity);
    }

    @Override
    public Optional<Product> findById(ProductId productId) {
        ProductEntity entity = productMapper.selectById(productId.getValue());
        return entity != null ? Optional.of(convertToDomain(entity)) : Optional.empty();
    }

    @Override
    public Optional<Product> findByName(String name) {
        ProductEntity entity = productMapper.findByName(name);
        return entity != null ? Optional.of(convertToDomain(entity)) : Optional.empty();
    }

    @Override
    public List<Product> findByStatus(ProductStatus status) {
        List<ProductEntity> entities = productMapper.findByStatus(status.name());
        return entities.stream()
                .map(this::convertToDomain)
                .collect(Collectors.toList());
    }

    @Override
    public List<Product> findByCategoryId(Long categoryId) {
        List<ProductEntity> entities = productMapper.findByCategoryId(categoryId);
        return entities.stream()
                .map(this::convertToDomain)
                .collect(Collectors.toList());
    }

    @Override
    public List<Product> findAll(int page, int size) {
        int offset = (page - 1) * size;
        List<ProductEntity> entities = productMapper.findByConditions(
                null, null, null, null, null, 
                "create_time", "desc", offset, size);
        return entities.stream()
                .map(this::convertToDomain)
                .collect(Collectors.toList());
    }

    @Override
    public void delete(ProductId productId) {
        productMapper.deleteById(productId.getValue());
        log.debug("删除产品: {}", productId.getValue());
    }

    @Override
    public boolean existsByName(String name) {
        return productMapper.countByName(name) > 0;
    }

    @Override
    public long count() {
        return productMapper.selectCount(null);
    }

    @Override
    public long countByStatus(ProductStatus status) {
        return productMapper.countByStatus(status.name());
    }

    /**
     * 领域对象转换为数据库实体
     */
    private ProductEntity convertToEntity(Product product) {
        ProductEntity entity = new ProductEntity();
        
        if (product.getId() != null) {
            entity.setId(product.getId().getValue());
        }
        
        entity.setName(product.getName().getValue());
        entity.setDescription(product.getDescription());
        entity.setPrice(product.getPrice().getAmount());
        entity.setCurrency(product.getPrice().getCurrency().getCurrencyCode());
        entity.setStatus(product.getStatus().name());
        entity.setCategoryId(product.getCategory().getId());
        entity.setCategoryName(product.getCategory().getName());
        entity.setStock(product.getStock());
        
        // 转换图片列表为JSON
        if (product.getImages() != null && !product.getImages().isEmpty()) {
            entity.setImages(JsonUtils.toJson(product.getImages()));
        }
        
        // 转换属性列表为JSON
        if (product.getAttributes() != null && !product.getAttributes().isEmpty()) {
            Map<String, String> attributeMap = product.getAttributes().stream()
                    .collect(Collectors.toMap(
                            ProductAttribute::getName,
                            ProductAttribute::getValue
                    ));
            entity.setAttributes(JsonUtils.toJson(attributeMap));
        }
        
        return entity;
    }

    /**
     * 数据库实体转换为领域对象
     */
    private Product convertToDomain(ProductEntity entity) {
        // 创建产品
        Product product = new Product(
                ProductId.of(entity.getId()),
                ProductName.of(entity.getName()),
                entity.getDescription(),
                new Money(entity.getPrice(), Currency.getInstance(entity.getCurrency())),
                ProductCategory.of(entity.getCategoryId(), entity.getCategoryName()),
                entity.getStock()
        );
        
        // 设置状态
        product = setProductStatus(product, ProductStatus.valueOf(entity.getStatus()));
        
        // 添加图片
        if (entity.getImages() != null && !entity.getImages().isEmpty()) {
            List<String> images = JsonUtils.fromJsonToList(entity.getImages(), String.class);
            if (images != null) {
                images.forEach(product::addImage);
            }
        }
        
        // 添加属性
        if (entity.getAttributes() != null && !entity.getAttributes().isEmpty()) {
            Map<String, String> attributes = JsonUtils.fromJsonToMap(entity.getAttributes());
            if (attributes != null) {
                attributes.forEach((name, value) -> 
                        product.addAttribute(ProductAttribute.of(name, value)));
            }
        }
        
        return product;
    }

    /**
     * 设置产品状态（通过反射或其他方式）
     * 这里简化处理，实际项目中可能需要更复杂的状态设置逻辑
     */
    private Product setProductStatus(Product product, ProductStatus status) {
        // 由于Product的状态字段是私有的，这里需要根据实际情况处理
        // 可以通过反射或者在Product类中添加设置状态的方法
        return product;
    }
}
