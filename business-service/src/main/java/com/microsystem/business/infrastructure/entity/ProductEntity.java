package com.microsystem.business.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.microsystem.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 产品数据库实体
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_product")
public class ProductEntity extends BaseEntity {

    /**
     * 产品名称
     */
    @TableField("name")
    private String name;

    /**
     * 产品描述
     */
    @TableField("description")
    private String description;

    /**
     * 产品价格
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * 货币代码
     */
    @TableField("currency")
    private String currency;

    /**
     * 产品状态
     */
    @TableField("status")
    private String status;

    /**
     * 分类ID
     */
    @TableField("category_id")
    private Long categoryId;

    /**
     * 分类名称
     */
    @TableField("category_name")
    private String categoryName;

    /**
     * 库存数量
     */
    @TableField("stock")
    private Integer stock;

    /**
     * 产品图片（JSON格式存储）
     */
    @TableField("images")
    private String images;

    /**
     * 产品属性（JSON格式存储）
     */
    @TableField("attributes")
    private String attributes;

    /**
     * 推荐度评分
     */
    @TableField("recommendation_score")
    private Double recommendationScore;
}
