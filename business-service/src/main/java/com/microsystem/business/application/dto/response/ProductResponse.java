package com.microsystem.business.application.dto.response;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 产品响应DTO
 * 
 * <AUTHOR>
 */
@Data
public class ProductResponse {

    /**
     * 产品ID
     */
    private Long id;

    /**
     * 产品名称
     */
    private String name;

    /**
     * 产品描述
     */
    private String description;

    /**
     * 产品价格
     */
    private BigDecimal price;

    /**
     * 货币
     */
    private String currency;

    /**
     * 产品状态
     */
    private String status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 库存数量
     */
    private Integer stock;

    /**
     * 产品图片
     */
    private List<String> images;

    /**
     * 产品属性
     */
    private Map<String, String> attributes;

    /**
     * 推荐度评分
     */
    private Double recommendationScore;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
