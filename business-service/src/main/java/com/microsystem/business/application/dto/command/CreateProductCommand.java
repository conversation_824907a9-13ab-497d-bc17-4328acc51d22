package com.microsystem.business.application.dto.command;

import jakarta.validation.constraints.*;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 创建产品命令
 * 
 * <AUTHOR>
 */
@Data
public class CreateProductCommand {

    /**
     * 产品名称
     */
    @NotBlank(message = "产品名称不能为空")
    @Size(max = 100, message = "产品名称长度不能超过100个字符")
    private String name;

    /**
     * 产品描述
     */
    @Size(max = 1000, message = "产品描述长度不能超过1000个字符")
    private String description;

    /**
     * 产品价格
     */
    @NotNull(message = "产品价格不能为空")
    @DecimalMin(value = "0.01", message = "产品价格必须大于0")
    @Digits(integer = 10, fraction = 2, message = "价格格式不正确")
    private BigDecimal price;

    /**
     * 产品分类ID
     */
    @NotNull(message = "产品分类不能为空")
    @Positive(message = "分类ID必须大于0")
    private Long categoryId;

    /**
     * 产品分类名称
     */
    @NotBlank(message = "产品分类名称不能为空")
    private String categoryName;

    /**
     * 库存数量
     */
    @NotNull(message = "库存数量不能为空")
    @Min(value = 0, message = "库存数量不能为负数")
    private Integer stock;

    /**
     * 产品图片
     */
    private List<String> images;

    /**
     * 产品属性
     */
    private List<ProductAttributeDto> attributes;

    /**
     * 产品属性DTO
     */
    @Data
    public static class ProductAttributeDto {
        @NotBlank(message = "属性名称不能为空")
        private String name;

        @NotBlank(message = "属性值不能为空")
        private String value;
    }
}
