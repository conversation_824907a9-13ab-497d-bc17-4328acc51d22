package com.microsystem.business.application.dto.command;

import jakarta.validation.constraints.*;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 更新产品命令
 * 
 * <AUTHOR>
 */
@Data
public class UpdateProductCommand {

    /**
     * 产品名称
     */
    @Size(max = 100, message = "产品名称长度不能超过100个字符")
    private String name;

    /**
     * 产品描述
     */
    @Size(max = 1000, message = "产品描述长度不能超过1000个字符")
    private String description;

    /**
     * 产品价格
     */
    @DecimalMin(value = "0.01", message = "产品价格必须大于0")
    @Digits(integer = 10, fraction = 2, message = "价格格式不正确")
    private BigDecimal price;

    /**
     * 库存数量
     */
    @Min(value = 0, message = "库存数量不能为负数")
    private Integer stock;
}
