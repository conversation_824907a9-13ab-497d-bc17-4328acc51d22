package com.microsystem.business.application.dto.query;

import lombok.Data;

/**
 * 产品查询条件
 * 
 * <AUTHOR>
 */
@Data
public class ProductQuery {

    /**
     * 页码
     */
    private int page = 1;

    /**
     * 每页大小
     */
    private int size = 10;

    /**
     * 产品名称（模糊查询）
     */
    private String name;

    /**
     * 产品状态
     */
    private String status;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 最小价格
     */
    private Double minPrice;

    /**
     * 最大价格
     */
    private Double maxPrice;

    /**
     * 排序字段
     */
    private String sortBy = "createdAt";

    /**
     * 排序方向
     */
    private String sortDirection = "desc";
}
