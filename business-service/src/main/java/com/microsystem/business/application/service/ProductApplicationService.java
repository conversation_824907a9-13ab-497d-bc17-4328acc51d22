package com.microsystem.business.application.service;

import com.microsystem.business.application.dto.command.CreateProductCommand;
import com.microsystem.business.application.dto.command.UpdateProductCommand;
import com.microsystem.business.application.dto.query.ProductQuery;
import com.microsystem.business.application.dto.response.ProductResponse;
import com.microsystem.business.domain.model.product.*;
import com.microsystem.business.domain.model.shared.Money;
import com.microsystem.business.domain.repository.ProductRepository;
import com.microsystem.business.domain.service.ProductDomainService;
import com.microsystem.common.exception.BusinessException;
import com.microsystem.common.result.PageResult;
import com.microsystem.common.result.ResultCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 产品应用服务
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class ProductApplicationService {

    private final ProductRepository productRepository;
    private final ProductDomainService productDomainService;

    /**
     * 创建产品
     */
    public ProductResponse createProduct(CreateProductCommand command) {
        log.info("创建产品: {}", command.getName());

        // 检查产品名称是否唯一
        ProductName productName = ProductName.of(command.getName());
        if (!productDomainService.isProductNameUnique(productName)) {
            throw new BusinessException(ResultCode.DATA_ALREADY_EXISTS, "产品名称已存在");
        }

        // 创建产品
        Product product = new Product(
            ProductId.of(System.currentTimeMillis()), // 临时ID生成策略
            productName,
            command.getDescription(),
            Money.of(command.getPrice()),
            ProductCategory.of(command.getCategoryId(), command.getCategoryName()),
            command.getStock()
        );

        // 添加产品属性
        if (command.getAttributes() != null) {
            command.getAttributes().forEach(attr -> 
                product.addAttribute(ProductAttribute.of(attr.getName(), attr.getValue()))
            );
        }

        // 添加产品图片
        if (command.getImages() != null) {
            command.getImages().forEach(product::addImage);
        }

        // 保存产品
        Product savedProduct = productRepository.save(product);

        log.info("产品创建成功: {}", savedProduct.getId());
        return convertToResponse(savedProduct);
    }

    /**
     * 更新产品
     */
    public ProductResponse updateProduct(Long productId, UpdateProductCommand command) {
        log.info("更新产品: {}", productId);

        Product product = productRepository.findById(ProductId.of(productId))
            .orElseThrow(() -> new BusinessException(ResultCode.DATA_NOT_FOUND, "产品不存在"));

        // 检查产品名称是否唯一
        if (command.getName() != null) {
            ProductName newName = ProductName.of(command.getName());
            if (!productDomainService.isProductNameUnique(newName, product)) {
                throw new BusinessException(ResultCode.DATA_ALREADY_EXISTS, "产品名称已存在");
            }
        }

        // 更新产品信息
        if (command.getName() != null || command.getDescription() != null || command.getPrice() != null) {
            product.updateInfo(
                command.getName() != null ? ProductName.of(command.getName()) : product.getName(),
                command.getDescription() != null ? command.getDescription() : product.getDescription(),
                command.getPrice() != null ? Money.of(command.getPrice()) : product.getPrice()
            );
        }

        // 更新库存
        if (command.getStock() != null) {
            int stockDiff = command.getStock() - product.getStock();
            if (stockDiff > 0) {
                product.increaseStock(stockDiff);
            } else if (stockDiff < 0) {
                product.decreaseStock(-stockDiff);
            }
        }

        // 保存产品
        Product savedProduct = productRepository.save(product);

        log.info("产品更新成功: {}", savedProduct.getId());
        return convertToResponse(savedProduct);
    }

    /**
     * 发布产品
     */
    public void publishProduct(Long productId) {
        log.info("发布产品: {}", productId);

        Product product = productRepository.findById(ProductId.of(productId))
            .orElseThrow(() -> new BusinessException(ResultCode.DATA_NOT_FOUND, "产品不存在"));

        // 验证产品是否可以发布
        productDomainService.validateProductForPublish(product);

        // 发布产品
        product.publish();

        // 保存产品
        productRepository.save(product);

        log.info("产品发布成功: {}", productId);
    }

    /**
     * 下架产品
     */
    public void unpublishProduct(Long productId) {
        log.info("下架产品: {}", productId);

        Product product = productRepository.findById(ProductId.of(productId))
            .orElseThrow(() -> new BusinessException(ResultCode.DATA_NOT_FOUND, "产品不存在"));

        // 下架产品
        product.unpublish();

        // 保存产品
        productRepository.save(product);

        log.info("产品下架成功: {}", productId);
    }

    /**
     * 查询产品详情
     */
    @Transactional(readOnly = true)
    public ProductResponse getProduct(Long productId) {
        Product product = productRepository.findById(ProductId.of(productId))
            .orElseThrow(() -> new BusinessException(ResultCode.DATA_NOT_FOUND, "产品不存在"));

        return convertToResponse(product);
    }

    /**
     * 分页查询产品列表
     */
    @Transactional(readOnly = true)
    public PageResult<ProductResponse> getProducts(ProductQuery query) {
        List<Product> products = productRepository.findAll(query.getPage(), query.getSize());
        long total = productRepository.count();

        List<ProductResponse> responses = products.stream()
            .map(this::convertToResponse)
            .collect(Collectors.toList());

        return PageResult.of((long) query.getPage(), (long) query.getSize(), total, responses);
    }

    /**
     * 删除产品
     */
    public void deleteProduct(Long productId) {
        log.info("删除产品: {}", productId);

        Product product = productRepository.findById(ProductId.of(productId))
            .orElseThrow(() -> new BusinessException(ResultCode.DATA_NOT_FOUND, "产品不存在"));

        // 检查产品状态
        if (product.getStatus() == ProductStatus.PUBLISHED) {
            throw new BusinessException(ResultCode.OPERATION_NOT_ALLOWED, "已发布的产品不能删除，请先下架");
        }

        productRepository.delete(ProductId.of(productId));

        log.info("产品删除成功: {}", productId);
    }

    /**
     * 转换为响应对象
     */
    private ProductResponse convertToResponse(Product product) {
        ProductResponse response = new ProductResponse();
        response.setId(product.getId().getValue());
        response.setName(product.getName().getValue());
        response.setDescription(product.getDescription());
        response.setPrice(product.getPrice().getAmount());
        response.setCurrency(product.getPrice().getCurrency().getCurrencyCode());
        response.setStatus(product.getStatus().name());
        response.setStatusDesc(product.getStatus().getDescription());
        response.setCategoryId(product.getCategory().getId());
        response.setCategoryName(product.getCategory().getName());
        response.setStock(product.getStock());
        response.setImages(product.getImages());
        response.setCreatedAt(product.getCreatedAt());
        response.setUpdatedAt(product.getUpdatedAt());

        if (product.getAttributes() != null) {
            response.setAttributes(product.getAttributes().stream()
                .collect(Collectors.toMap(
                    ProductAttribute::getName,
                    ProductAttribute::getValue
                )));
        }

        // 计算推荐度
        response.setRecommendationScore(productDomainService.calculateProductRecommendationScore(product));

        return response;
    }
}
