package com.microsystem.business;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 业务服务启动类
 * 
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = "com.microsystem")
@EnableDiscoveryClient
@EnableTransactionManagement
@MapperScan("com.microsystem.business.infrastructure.mapper")
public class BusinessServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(BusinessServiceApplication.class, args);
    }
}
