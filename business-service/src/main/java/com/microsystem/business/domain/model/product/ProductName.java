package com.microsystem.business.domain.model.product;

import com.microsystem.business.domain.model.shared.ValueObject;
import lombok.Getter;

import java.util.Objects;

/**
 * 产品名称值对象
 * 
 * <AUTHOR>
 */
@Getter
public class ProductName implements ValueObject {

    private final String value;

    public ProductName(String value) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("产品名称不能为空");
        }
        if (value.length() > 100) {
            throw new IllegalArgumentException("产品名称长度不能超过100个字符");
        }
        this.value = value.trim();
    }

    public static ProductName of(String value) {
        return new ProductName(value);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        ProductName that = (ProductName) obj;
        return Objects.equals(value, that.value);
    }

    @Override
    public int hashCode() {
        return Objects.hash(value);
    }

    @Override
    public String toString() {
        return value;
    }
}
