package com.microsystem.business.domain.service;

import com.microsystem.business.domain.model.product.Product;
import com.microsystem.business.domain.model.product.ProductName;
import com.microsystem.business.domain.repository.ProductRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 产品领域服务
 * 
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ProductDomainService {

    private final ProductRepository productRepository;

    /**
     * 检查产品名称是否唯一
     */
    public boolean isProductNameUnique(ProductName productName) {
        return !productRepository.existsByName(productName.getValue());
    }

    /**
     * 检查产品名称是否唯一（排除指定产品）
     */
    public boolean isProductNameUnique(ProductName productName, Product excludeProduct) {
        if (excludeProduct.getName().equals(productName)) {
            return true; // 如果是同一个产品的名称，则认为是唯一的
        }
        return !productRepository.existsByName(productName.getValue());
    }

    /**
     * 验证产品是否可以发布
     */
    public void validateProductForPublish(Product product) {
        if (product.getPrice() == null || product.getPrice().getAmount().doubleValue() <= 0) {
            throw new IllegalStateException("产品价格必须大于0才能发布");
        }
        
        if (product.getStock() == null || product.getStock() <= 0) {
            throw new IllegalStateException("产品库存必须大于0才能发布");
        }
        
        if (product.getDescription() == null || product.getDescription().trim().isEmpty()) {
            throw new IllegalStateException("产品描述不能为空");
        }
        
        if (product.getCategory() == null) {
            throw new IllegalStateException("产品必须指定分类");
        }
    }

    /**
     * 计算产品推荐度
     */
    public double calculateProductRecommendationScore(Product product) {
        double score = 0.0;
        
        // 基础分数
        score += 10.0;
        
        // 根据库存情况加分
        if (product.getStock() > 100) {
            score += 20.0;
        } else if (product.getStock() > 50) {
            score += 15.0;
        } else if (product.getStock() > 10) {
            score += 10.0;
        }
        
        // 根据图片数量加分
        if (product.getImages() != null) {
            score += Math.min(product.getImages().size() * 5, 25);
        }
        
        // 根据属性数量加分
        if (product.getAttributes() != null) {
            score += Math.min(product.getAttributes().size() * 3, 15);
        }
        
        // 根据描述长度加分
        if (product.getDescription() != null) {
            int descLength = product.getDescription().length();
            if (descLength > 500) {
                score += 15.0;
            } else if (descLength > 200) {
                score += 10.0;
            } else if (descLength > 50) {
                score += 5.0;
            }
        }
        
        return Math.min(score, 100.0); // 最高100分
    }
}
