package com.microsystem.business.domain.model.shared;

import lombok.Getter;

import java.util.Objects;

/**
 * 实体基类
 * 
 * <AUTHOR>
 */
@Getter
public abstract class Entity<ID extends ValueObject> {

    /**
     * 实体ID
     */
    private ID id;

    protected Entity() {
        // JPA需要
    }

    protected Entity(ID id) {
        this.id = Objects.requireNonNull(id, "实体ID不能为空");
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        Entity<?> entity = (Entity<?>) obj;
        return Objects.equals(id, entity.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return String.format("%s{id=%s}", getClass().getSimpleName(), id);
    }
}
