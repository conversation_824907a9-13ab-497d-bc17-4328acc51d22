package com.microsystem.business.domain.model.product;

import com.microsystem.business.domain.model.shared.ValueObject;
import lombok.Getter;

import java.util.Objects;

/**
 * 产品ID值对象
 * 
 * <AUTHOR>
 */
@Getter
public class ProductId implements ValueObject {

    private final Long value;

    public ProductId(Long value) {
        this.value = Objects.requireNonNull(value, "产品ID不能为空");
        if (value <= 0) {
            throw new IllegalArgumentException("产品ID必须大于0");
        }
    }

    public static ProductId of(Long value) {
        return new ProductId(value);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        ProductId productId = (ProductId) obj;
        return Objects.equals(value, productId.value);
    }

    @Override
    public int hashCode() {
        return Objects.hash(value);
    }

    @Override
    public String toString() {
        return "ProductId{" + value + "}";
    }
}
