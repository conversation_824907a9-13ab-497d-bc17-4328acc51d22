package com.microsystem.business.domain.model.product;

/**
 * 产品状态枚举
 * 
 * <AUTHOR>
 */
public enum ProductStatus {

    /**
     * 草稿
     */
    DRAFT("草稿"),

    /**
     * 已发布
     */
    PUBLISHED("已发布"),

    /**
     * 已下架
     */
    UNPUBLISHED("已下架"),

    /**
     * 已删除
     */
    DELETED("已删除");

    private final String description;

    ProductStatus(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}
