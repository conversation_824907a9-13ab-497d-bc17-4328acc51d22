package com.microsystem.business.domain.repository;

import com.microsystem.business.domain.model.product.Product;
import com.microsystem.business.domain.model.product.ProductId;
import com.microsystem.business.domain.model.product.ProductStatus;

import java.util.List;
import java.util.Optional;

/**
 * 产品仓储接口
 * 
 * <AUTHOR>
 */
public interface ProductRepository {

    /**
     * 保存产品
     */
    Product save(Product product);

    /**
     * 根据ID查找产品
     */
    Optional<Product> findById(ProductId productId);

    /**
     * 根据名称查找产品
     */
    Optional<Product> findByName(String name);

    /**
     * 根据状态查找产品列表
     */
    List<Product> findByStatus(ProductStatus status);

    /**
     * 根据分类查找产品列表
     */
    List<Product> findByCategoryId(Long categoryId);

    /**
     * 分页查询产品
     */
    List<Product> findAll(int page, int size);

    /**
     * 删除产品
     */
    void delete(ProductId productId);

    /**
     * 检查产品名称是否存在
     */
    boolean existsByName(String name);

    /**
     * 统计产品数量
     */
    long count();

    /**
     * 统计指定状态的产品数量
     */
    long countByStatus(ProductStatus status);
}
