package com.microsystem.business.domain.model.shared;

import lombok.Getter;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Currency;
import java.util.Objects;

/**
 * 金额值对象
 * 
 * <AUTHOR>
 */
@Getter
public class Money implements ValueObject {

    /**
     * 金额
     */
    private final BigDecimal amount;

    /**
     * 货币
     */
    private final Currency currency;

    /**
     * 默认货币（人民币）
     */
    public static final Currency DEFAULT_CURRENCY = Currency.getInstance("CNY");

    /**
     * 零元
     */
    public static final Money ZERO = new Money(BigDecimal.ZERO, DEFAULT_CURRENCY);

    /**
     * 构造函数
     */
    public Money(BigDecimal amount, Currency currency) {
        this.amount = Objects.requireNonNull(amount, "金额不能为空").setScale(2, RoundingMode.HALF_UP);
        this.currency = Objects.requireNonNull(currency, "货币不能为空");
        
        if (amount.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("金额不能为负数");
        }
    }

    /**
     * 构造函数（使用默认货币）
     */
    public Money(BigDecimal amount) {
        this(amount, DEFAULT_CURRENCY);
    }

    /**
     * 构造函数（使用double）
     */
    public Money(double amount) {
        this(BigDecimal.valueOf(amount), DEFAULT_CURRENCY);
    }

    /**
     * 创建金额
     */
    public static Money of(BigDecimal amount) {
        return new Money(amount);
    }

    /**
     * 创建金额
     */
    public static Money of(double amount) {
        return new Money(amount);
    }

    /**
     * 加法
     */
    public Money add(Money other) {
        checkCurrency(other);
        return new Money(this.amount.add(other.amount), this.currency);
    }

    /**
     * 减法
     */
    public Money subtract(Money other) {
        checkCurrency(other);
        BigDecimal result = this.amount.subtract(other.amount);
        if (result.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("减法结果不能为负数");
        }
        return new Money(result, this.currency);
    }

    /**
     * 乘法
     */
    public Money multiply(BigDecimal multiplier) {
        return new Money(this.amount.multiply(multiplier), this.currency);
    }

    /**
     * 除法
     */
    public Money divide(BigDecimal divisor) {
        if (divisor.compareTo(BigDecimal.ZERO) == 0) {
            throw new IllegalArgumentException("除数不能为零");
        }
        return new Money(this.amount.divide(divisor, 2, RoundingMode.HALF_UP), this.currency);
    }

    /**
     * 比较大小
     */
    public int compareTo(Money other) {
        checkCurrency(other);
        return this.amount.compareTo(other.amount);
    }

    /**
     * 是否大于
     */
    public boolean greaterThan(Money other) {
        return compareTo(other) > 0;
    }

    /**
     * 是否小于
     */
    public boolean lessThan(Money other) {
        return compareTo(other) < 0;
    }

    /**
     * 是否等于
     */
    public boolean equalTo(Money other) {
        return compareTo(other) == 0;
    }

    /**
     * 检查货币是否相同
     */
    private void checkCurrency(Money other) {
        if (!this.currency.equals(other.currency)) {
            throw new IllegalArgumentException("货币类型不匹配");
        }
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Money money = (Money) obj;
        return Objects.equals(amount, money.amount) && Objects.equals(currency, money.currency);
    }

    @Override
    public int hashCode() {
        return Objects.hash(amount, currency);
    }

    @Override
    public String toString() {
        return String.format("%s %s", currency.getCurrencyCode(), amount);
    }
}
