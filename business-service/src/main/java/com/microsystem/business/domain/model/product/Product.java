package com.microsystem.business.domain.model.product;

import com.microsystem.business.domain.model.shared.AggregateRoot;
import com.microsystem.business.domain.model.shared.Money;
import lombok.Getter;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 产品聚合根
 * 
 * <AUTHOR>
 */
@Getter
public class Product extends AggregateRoot<ProductId> {

    /**
     * 产品名称
     */
    private ProductName name;

    /**
     * 产品描述
     */
    private String description;

    /**
     * 产品价格
     */
    private Money price;

    /**
     * 产品状态
     */
    private ProductStatus status;

    /**
     * 产品分类
     */
    private ProductCategory category;

    /**
     * 库存数量
     */
    private Integer stock;

    /**
     * 产品图片
     */
    private List<String> images;

    /**
     * 产品属性
     */
    private List<ProductAttribute> attributes;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 构造函数
     */
    protected Product() {
        // JPA需要
    }

    /**
     * 创建产品
     */
    public Product(ProductId id, ProductName name, String description, Money price, 
                   ProductCategory category, Integer stock) {
        super(id);
        this.name = name;
        this.description = description;
        this.price = price;
        this.category = category;
        this.stock = stock;
        this.status = ProductStatus.DRAFT;
        this.images = new ArrayList<>();
        this.attributes = new ArrayList<>();
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 更新产品信息
     */
    public void updateInfo(ProductName name, String description, Money price) {
        this.name = name;
        this.description = description;
        this.price = price;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 上架产品
     */
    public void publish() {
        if (this.status == ProductStatus.DRAFT) {
            this.status = ProductStatus.PUBLISHED;
            this.updatedAt = LocalDateTime.now();
            // 发布领域事件
            addDomainEvent(new ProductPublishedEvent(this.getId(), this.name.getValue()));
        } else {
            throw new IllegalStateException("只有草稿状态的产品才能上架");
        }
    }

    /**
     * 下架产品
     */
    public void unpublish() {
        if (this.status == ProductStatus.PUBLISHED) {
            this.status = ProductStatus.UNPUBLISHED;
            this.updatedAt = LocalDateTime.now();
            // 发布领域事件
            addDomainEvent(new ProductUnpublishedEvent(this.getId(), this.name.getValue()));
        } else {
            throw new IllegalStateException("只有已上架的产品才能下架");
        }
    }

    /**
     * 增加库存
     */
    public void increaseStock(Integer quantity) {
        if (quantity <= 0) {
            throw new IllegalArgumentException("增加库存数量必须大于0");
        }
        this.stock += quantity;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 减少库存
     */
    public void decreaseStock(Integer quantity) {
        if (quantity <= 0) {
            throw new IllegalArgumentException("减少库存数量必须大于0");
        }
        if (this.stock < quantity) {
            throw new IllegalStateException("库存不足");
        }
        this.stock -= quantity;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 添加产品图片
     */
    public void addImage(String imageUrl) {
        if (imageUrl == null || imageUrl.trim().isEmpty()) {
            throw new IllegalArgumentException("图片URL不能为空");
        }
        this.images.add(imageUrl);
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 添加产品属性
     */
    public void addAttribute(ProductAttribute attribute) {
        if (attribute == null) {
            throw new IllegalArgumentException("产品属性不能为空");
        }
        this.attributes.add(attribute);
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 检查是否可以购买
     */
    public boolean canBePurchased() {
        return this.status == ProductStatus.PUBLISHED && this.stock > 0;
    }
}
