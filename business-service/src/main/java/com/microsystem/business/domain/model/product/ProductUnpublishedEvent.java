package com.microsystem.business.domain.model.product;

import com.microsystem.business.domain.model.shared.DomainEvent;
import lombok.Getter;

import java.time.LocalDateTime;

/**
 * 产品下架事件
 * 
 * <AUTHOR>
 */
@Getter
public class ProductUnpublishedEvent implements DomainEvent {

    private final ProductId productId;
    private final String productName;
    private final LocalDateTime occurredOn;

    public ProductUnpublishedEvent(ProductId productId, String productName) {
        this.productId = productId;
        this.productName = productName;
        this.occurredOn = LocalDateTime.now();
    }

    @Override
    public LocalDateTime getOccurredOn() {
        return occurredOn;
    }

    @Override
    public String getEventType() {
        return "ProductUnpublished";
    }
}
