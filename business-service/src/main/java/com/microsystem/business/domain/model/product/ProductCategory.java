package com.microsystem.business.domain.model.product;

import com.microsystem.business.domain.model.shared.ValueObject;
import lombok.Getter;

import java.util.Objects;

/**
 * 产品分类值对象
 * 
 * <AUTHOR>
 */
@Getter
public class ProductCategory implements ValueObject {

    private final Long id;
    private final String name;

    public ProductCategory(Long id, String name) {
        this.id = Objects.requireNonNull(id, "分类ID不能为空");
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("分类名称不能为空");
        }
        this.name = name.trim();
    }

    public static ProductCategory of(Long id, String name) {
        return new ProductCategory(id, name);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        ProductCategory that = (ProductCategory) obj;
        return Objects.equals(id, that.id) && Objects.equals(name, that.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, name);
    }

    @Override
    public String toString() {
        return String.format("ProductCategory{id=%d, name='%s'}", id, name);
    }
}
