package com.microsystem.business.domain.model.product;

import com.microsystem.business.domain.model.shared.ValueObject;
import lombok.Getter;

import java.util.Objects;

/**
 * 产品属性值对象
 * 
 * <AUTHOR>
 */
@Getter
public class ProductAttribute implements ValueObject {

    private final String name;
    private final String value;

    public ProductAttribute(String name, String value) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("属性名称不能为空");
        }
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("属性值不能为空");
        }
        this.name = name.trim();
        this.value = value.trim();
    }

    public static ProductAttribute of(String name, String value) {
        return new ProductAttribute(name, value);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        ProductAttribute that = (ProductAttribute) obj;
        return Objects.equals(name, that.name) && Objects.equals(value, that.value);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, value);
    }

    @Override
    public String toString() {
        return String.format("ProductAttribute{name='%s', value='%s'}", name, value);
    }
}
