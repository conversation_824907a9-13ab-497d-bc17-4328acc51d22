<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds">
    
    <!-- 引入Spring Boot默认配置 -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    
    <!-- 定义日志文件路径 -->
    <springProfile name="!docker">
        <property name="LOG_PATH" value="./logs/business-service"/>
    </springProfile>
    <springProfile name="docker">
        <property name="LOG_PATH" value="/app/logs"/>
    </springProfile>
    
    <!-- 定义日志格式 -->
    <property name="LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-}] [%X{userId:-}] %logger{50} - %msg%n"/>
    
    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
    </appender>
    
    <!-- 异步控制台输出 -->
    <appender name="ASYNC_CONSOLE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="CONSOLE"/>
        <queueSize>1024</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <maxFlushTime>2000</maxFlushTime>
        <neverBlock>true</neverBlock>
    </appender>
    
    <!-- 应用日志文件 -->
    <appender name="APP_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/business-service.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/business-service.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    
    <!-- 异步应用日志 -->
    <appender name="ASYNC_APP_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="APP_FILE"/>
        <queueSize>2048</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <maxFlushTime>2000</maxFlushTime>
        <neverBlock>true</neverBlock>
    </appender>
    
    <!-- 错误日志文件 -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/business-service-error.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/business-service-error.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>60</maxHistory>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    
    <!-- 异步错误日志 -->
    <appender name="ASYNC_ERROR_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="ERROR_FILE"/>
        <queueSize>1024</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <maxFlushTime>2000</maxFlushTime>
        <neverBlock>true</neverBlock>
    </appender>
    
    <!-- 业务关键操作日志 -->
    <appender name="BUSINESS_CRITICAL_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/business-service-critical.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/business-service-critical.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>90</maxHistory>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- 异步业务关键操作日志 -->
    <appender name="ASYNC_BUSINESS_CRITICAL_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="BUSINESS_CRITICAL_FILE"/>
        <queueSize>2048</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <maxFlushTime>2000</maxFlushTime>
        <neverBlock>true</neverBlock>
    </appender>
    
    <!-- 性能监控日志 -->
    <appender name="PERFORMANCE_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/business-service-performance.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/business-service-performance.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- 异步性能监控日志 -->
    <appender name="ASYNC_PERFORMANCE_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="PERFORMANCE_FILE"/>
        <queueSize>2048</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <maxFlushTime>1000</maxFlushTime>
        <neverBlock>true</neverBlock>
    </appender>
    
    <!-- 数据库操作日志 -->
    <appender name="DATABASE_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/business-service-database.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/business-service-database.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>200MB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>2GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- 异步数据库操作日志 -->
    <appender name="ASYNC_DATABASE_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="DATABASE_FILE"/>
        <queueSize>4096</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <maxFlushTime>1000</maxFlushTime>
        <neverBlock>true</neverBlock>
    </appender>
    
    <!-- 消息队列日志 -->
    <appender name="MESSAGE_QUEUE_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/business-service-mq.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/business-service-mq.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- 异步消息队列日志 -->
    <appender name="ASYNC_MESSAGE_QUEUE_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="MESSAGE_QUEUE_FILE"/>
        <queueSize>2048</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <maxFlushTime>1000</maxFlushTime>
        <neverBlock>true</neverBlock>
    </appender>
    
    <!-- 特定Logger配置 -->
    <logger name="BUSINESS_CRITICAL" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_BUSINESS_CRITICAL_FILE"/>
    </logger>
    
    <logger name="PERFORMANCE" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_PERFORMANCE_FILE"/>
    </logger>
    
    <logger name="DATABASE" level="DEBUG" additivity="false">
        <appender-ref ref="ASYNC_DATABASE_FILE"/>
    </logger>
    
    <logger name="MESSAGE_QUEUE" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_MESSAGE_QUEUE_FILE"/>
    </logger>
    
    <!-- 应用包日志级别 -->
    <logger name="com.microsystem.business" level="INFO"/>
    <logger name="com.microsystem.common" level="INFO"/>
    
    <!-- MyBatis日志 -->
    <logger name="com.microsystem.business.infrastructure.mapper" level="DEBUG"/>
    
    <!-- 第三方库日志级别 -->
    <logger name="org.springframework" level="WARN"/>
    <logger name="org.apache" level="WARN"/>
    <logger name="com.alibaba" level="WARN"/>
    <logger name="io.lettuce" level="WARN"/>
    <logger name="com.zaxxer.hikari" level="WARN"/>
    <logger name="org.apache.shardingsphere" level="WARN"/>
    
    <!-- 根Logger配置 -->
    <springProfile name="dev,test">
        <root level="DEBUG">
            <appender-ref ref="ASYNC_CONSOLE"/>
            <appender-ref ref="ASYNC_APP_FILE"/>
            <appender-ref ref="ASYNC_ERROR_FILE"/>
        </root>
    </springProfile>
    
    <springProfile name="prod,docker">
        <root level="INFO">
            <appender-ref ref="ASYNC_APP_FILE"/>
            <appender-ref ref="ASYNC_ERROR_FILE"/>
        </root>
    </springProfile>
    
</configuration>
