package com.microsystem.business.application.service;

import com.microsystem.business.application.dto.command.CreateProductCommand;
import com.microsystem.business.application.dto.response.ProductResponse;
import com.microsystem.business.domain.model.product.*;
import com.microsystem.business.domain.model.shared.Money;
import com.microsystem.business.domain.repository.ProductRepository;
import com.microsystem.business.domain.service.ProductDomainService;
import com.microsystem.common.exception.BusinessException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 产品应用服务测试类
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class ProductApplicationServiceTest {

    @Mock
    private ProductRepository productRepository;

    @Mock
    private ProductDomainService productDomainService;

    @InjectMocks
    private ProductApplicationService productApplicationService;

    private CreateProductCommand createProductCommand;
    private Product testProduct;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        createProductCommand = new CreateProductCommand();
        createProductCommand.setName("测试产品");
        createProductCommand.setDescription("这是一个测试产品");
        createProductCommand.setPrice(new BigDecimal("99.99"));
        createProductCommand.setCategoryId(1L);
        createProductCommand.setCategoryName("电子产品");
        createProductCommand.setStock(100);
        createProductCommand.setImages(new ArrayList<>());
        createProductCommand.setAttributes(new ArrayList<>());

        testProduct = new Product(
                ProductId.of(1L),
                ProductName.of("测试产品"),
                "这是一个测试产品",
                Money.of(new BigDecimal("99.99")),
                ProductCategory.of(1L, "电子产品"),
                100
        );
    }

    @Test
    void testCreateProductSuccess() {
        // Given
        when(productDomainService.isProductNameUnique(any(ProductName.class))).thenReturn(true);
        when(productRepository.save(any(Product.class))).thenReturn(testProduct);

        // When
        ProductResponse response = productApplicationService.createProduct(createProductCommand);

        // Then
        assertNotNull(response);
        assertEquals("测试产品", response.getName());
        assertEquals("这是一个测试产品", response.getDescription());
        assertEquals(new BigDecimal("99.99"), response.getPrice());
        assertEquals("CNY", response.getCurrency());
        assertEquals("DRAFT", response.getStatus());
        assertEquals(1L, response.getCategoryId());
        assertEquals("电子产品", response.getCategoryName());
        assertEquals(100, response.getStock());

        verify(productDomainService).isProductNameUnique(any(ProductName.class));
        verify(productRepository).save(any(Product.class));
    }

    @Test
    void testCreateProductWithDuplicateName() {
        // Given
        when(productDomainService.isProductNameUnique(any(ProductName.class))).thenReturn(false);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            productApplicationService.createProduct(createProductCommand);
        });

        assertEquals("产品名称已存在", exception.getMessage());
        verify(productDomainService).isProductNameUnique(any(ProductName.class));
        verify(productRepository, never()).save(any(Product.class));
    }

    @Test
    void testGetProductSuccess() {
        // Given
        when(productRepository.findById(ProductId.of(1L))).thenReturn(Optional.of(testProduct));

        // When
        ProductResponse response = productApplicationService.getProduct(1L);

        // Then
        assertNotNull(response);
        assertEquals("测试产品", response.getName());
        assertEquals(1L, response.getId());

        verify(productRepository).findById(ProductId.of(1L));
    }

    @Test
    void testGetProductNotFound() {
        // Given
        when(productRepository.findById(ProductId.of(999L))).thenReturn(Optional.empty());

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            productApplicationService.getProduct(999L);
        });

        assertEquals("产品不存在", exception.getMessage());
        verify(productRepository).findById(ProductId.of(999L));
    }

    @Test
    void testPublishProductSuccess() {
        // Given
        when(productRepository.findById(ProductId.of(1L))).thenReturn(Optional.of(testProduct));
        doNothing().when(productDomainService).validateProductForPublish(testProduct);
        when(productRepository.save(any(Product.class))).thenReturn(testProduct);

        // When
        assertDoesNotThrow(() -> {
            productApplicationService.publishProduct(1L);
        });

        // Then
        verify(productRepository).findById(ProductId.of(1L));
        verify(productDomainService).validateProductForPublish(testProduct);
        verify(productRepository).save(any(Product.class));
    }

    @Test
    void testPublishProductNotFound() {
        // Given
        when(productRepository.findById(ProductId.of(999L))).thenReturn(Optional.empty());

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            productApplicationService.publishProduct(999L);
        });

        assertEquals("产品不存在", exception.getMessage());
        verify(productRepository).findById(ProductId.of(999L));
        verify(productDomainService, never()).validateProductForPublish(any(Product.class));
    }

    @Test
    void testPublishProductValidationFailed() {
        // Given
        when(productRepository.findById(ProductId.of(1L))).thenReturn(Optional.of(testProduct));
        doThrow(new IllegalStateException("产品价格必须大于0才能发布"))
                .when(productDomainService).validateProductForPublish(testProduct);

        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class, () -> {
            productApplicationService.publishProduct(1L);
        });

        assertEquals("产品价格必须大于0才能发布", exception.getMessage());
        verify(productRepository).findById(ProductId.of(1L));
        verify(productDomainService).validateProductForPublish(testProduct);
        verify(productRepository, never()).save(any(Product.class));
    }

    @Test
    void testDeleteProductSuccess() {
        // Given
        Product draftProduct = new Product(
                ProductId.of(1L),
                ProductName.of("草稿产品"),
                "草稿状态的产品",
                Money.of(new BigDecimal("99.99")),
                ProductCategory.of(1L, "电子产品"),
                100
        );
        
        when(productRepository.findById(ProductId.of(1L))).thenReturn(Optional.of(draftProduct));
        doNothing().when(productRepository).delete(ProductId.of(1L));

        // When
        assertDoesNotThrow(() -> {
            productApplicationService.deleteProduct(1L);
        });

        // Then
        verify(productRepository).findById(ProductId.of(1L));
        verify(productRepository).delete(ProductId.of(1L));
    }

    @Test
    void testDeletePublishedProduct() {
        // Given
        // 创建已发布状态的产品
        Product publishedProduct = new Product(
                ProductId.of(1L),
                ProductName.of("已发布产品"),
                "已发布状态的产品",
                Money.of(new BigDecimal("99.99")),
                ProductCategory.of(1L, "电子产品"),
                100
        );
        // 模拟发布状态
        publishedProduct.publish();
        
        when(productRepository.findById(ProductId.of(1L))).thenReturn(Optional.of(publishedProduct));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            productApplicationService.deleteProduct(1L);
        });

        assertEquals("已发布的产品不能删除，请先下架", exception.getMessage());
        verify(productRepository).findById(ProductId.of(1L));
        verify(productRepository, never()).delete(any(ProductId.class));
    }
}
