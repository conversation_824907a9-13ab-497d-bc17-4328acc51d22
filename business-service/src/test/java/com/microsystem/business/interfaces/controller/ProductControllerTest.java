package com.microsystem.business.interfaces.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.microsystem.business.application.dto.command.CreateProductCommand;
import com.microsystem.business.application.dto.response.ProductResponse;
import com.microsystem.business.application.service.ProductApplicationService;
import com.microsystem.common.exception.BusinessException;
import com.microsystem.common.result.ResultCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 产品控制器测试类
 * 
 * <AUTHOR>
 */
@WebMvcTest(ProductController.class)
class ProductControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ProductApplicationService productApplicationService;

    @Autowired
    private ObjectMapper objectMapper;

    private CreateProductCommand createProductCommand;
    private ProductResponse productResponse;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        createProductCommand = new CreateProductCommand();
        createProductCommand.setName("测试产品");
        createProductCommand.setDescription("这是一个测试产品");
        createProductCommand.setPrice(new BigDecimal("99.99"));
        createProductCommand.setCategoryId(1L);
        createProductCommand.setCategoryName("电子产品");
        createProductCommand.setStock(100);
        createProductCommand.setImages(new ArrayList<>());
        createProductCommand.setAttributes(new ArrayList<>());

        productResponse = new ProductResponse();
        productResponse.setId(1L);
        productResponse.setName("测试产品");
        productResponse.setDescription("这是一个测试产品");
        productResponse.setPrice(new BigDecimal("99.99"));
        productResponse.setCurrency("CNY");
        productResponse.setStatus("DRAFT");
        productResponse.setStatusDesc("草稿");
        productResponse.setCategoryId(1L);
        productResponse.setCategoryName("电子产品");
        productResponse.setStock(100);
        productResponse.setImages(new ArrayList<>());
        productResponse.setRecommendationScore(50.0);
        productResponse.setCreatedAt(LocalDateTime.now());
        productResponse.setUpdatedAt(LocalDateTime.now());
    }

    @Test
    void testCreateProductSuccess() throws Exception {
        // Given
        when(productApplicationService.createProduct(any(CreateProductCommand.class)))
                .thenReturn(productResponse);

        // When & Then
        mockMvc.perform(post("/api/products")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createProductCommand)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("产品创建成功"))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.name").value("测试产品"))
                .andExpect(jsonPath("$.data.price").value(99.99))
                .andExpect(jsonPath("$.data.status").value("DRAFT"));

        verify(productApplicationService).createProduct(any(CreateProductCommand.class));
    }

    @Test
    void testCreateProductWithInvalidInput() throws Exception {
        // Given
        createProductCommand.setName(""); // 空名称
        createProductCommand.setPrice(new BigDecimal("-1")); // 负价格

        // When & Then
        mockMvc.perform(post("/api/products")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createProductCommand)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value(400));

        verify(productApplicationService, never()).createProduct(any(CreateProductCommand.class));
    }

    @Test
    void testCreateProductWithDuplicateName() throws Exception {
        // Given
        when(productApplicationService.createProduct(any(CreateProductCommand.class)))
                .thenThrow(new BusinessException(ResultCode.DATA_ALREADY_EXISTS, "产品名称已存在"));

        // When & Then
        mockMvc.perform(post("/api/products")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createProductCommand)))
                .andExpect(status().isConflict())
                .andExpect(jsonPath("$.code").value(409))
                .andExpect(jsonPath("$.message").value("产品名称已存在"));

        verify(productApplicationService).createProduct(any(CreateProductCommand.class));
    }

    @Test
    void testGetProductSuccess() throws Exception {
        // Given
        when(productApplicationService.getProduct(1L)).thenReturn(productResponse);

        // When & Then
        mockMvc.perform(get("/api/products/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.name").value("测试产品"));

        verify(productApplicationService).getProduct(1L);
    }

    @Test
    void testGetProductNotFound() throws Exception {
        // Given
        when(productApplicationService.getProduct(999L))
                .thenThrow(new BusinessException(ResultCode.DATA_NOT_FOUND, "产品不存在"));

        // When & Then
        mockMvc.perform(get("/api/products/999"))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.code").value(404))
                .andExpect(jsonPath("$.message").value("产品不存在"));

        verify(productApplicationService).getProduct(999L);
    }

    @Test
    void testPublishProductSuccess() throws Exception {
        // Given
        doNothing().when(productApplicationService).publishProduct(1L);

        // When & Then
        mockMvc.perform(post("/api/products/1/publish"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("产品发布成功"));

        verify(productApplicationService).publishProduct(1L);
    }

    @Test
    void testPublishProductNotFound() throws Exception {
        // Given
        doThrow(new BusinessException(ResultCode.DATA_NOT_FOUND, "产品不存在"))
                .when(productApplicationService).publishProduct(999L);

        // When & Then
        mockMvc.perform(post("/api/products/999/publish"))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.code").value(404))
                .andExpect(jsonPath("$.message").value("产品不存在"));

        verify(productApplicationService).publishProduct(999L);
    }

    @Test
    void testUnpublishProductSuccess() throws Exception {
        // Given
        doNothing().when(productApplicationService).unpublishProduct(1L);

        // When & Then
        mockMvc.perform(post("/api/products/1/unpublish"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("产品下架成功"));

        verify(productApplicationService).unpublishProduct(1L);
    }

    @Test
    void testDeleteProductSuccess() throws Exception {
        // Given
        doNothing().when(productApplicationService).deleteProduct(1L);

        // When & Then
        mockMvc.perform(delete("/api/products/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("产品删除成功"));

        verify(productApplicationService).deleteProduct(1L);
    }

    @Test
    void testDeletePublishedProduct() throws Exception {
        // Given
        doThrow(new BusinessException(ResultCode.OPERATION_NOT_ALLOWED, "已发布的产品不能删除，请先下架"))
                .when(productApplicationService).deleteProduct(1L);

        // When & Then
        mockMvc.perform(delete("/api/products/1"))
                .andExpect(status().isForbidden())
                .andExpect(jsonPath("$.code").value(403))
                .andExpect(jsonPath("$.message").value("已发布的产品不能删除，请先下架"));

        verify(productApplicationService).deleteProduct(1L);
    }
}
