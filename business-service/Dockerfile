# 业务服务Dockerfile
FROM openjdk:17-jdk-slim

# 维护者信息
LABEL maintainer="MicroSystem Team <<EMAIL>>"
LABEL service="business-service"
LABEL version="1.0.0"

# 设置环境变量
ENV LANG=C.UTF-8
ENV TZ=Asia/Shanghai
ENV JAVA_OPTS="-Xms1024m -Xmx2048m -XX:MetaspaceSize=256m"
ENV SPRING_PROFILES_ACTIVE=docker
ENV SERVER_PORT=8082

# 安装必要工具
RUN apt-get update && \
    apt-get install -y --no-install-recommends curl netcat tzdata && \
    ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && \
    echo $TZ > /etc/timezone && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# 创建应用目录
WORKDIR /app

# 复制jar包
COPY target/business-service-*.jar app.jar

# 创建启动脚本
RUN echo '#!/bin/bash\n\
set -e\n\
\n\
# 等待依赖服务启动\n\
echo "Waiting for dependencies..."\n\
\n\
# 等待MySQL\n\
until nc -z mysql 3306; do\n\
  echo "Waiting for MySQL..."\n\
  sleep 2\n\
done\n\
\n\
# 等待Redis\n\
until nc -z redis 6379; do\n\
  echo "Waiting for Redis..."\n\
  sleep 2\n\
done\n\
\n\
# 等待Nacos\n\
until curl -f http://nacos:8848/nacos/v1/ns/operator/metrics; do\n\
  echo "Waiting for Nacos..."\n\
  sleep 5\n\
done\n\
\n\
# 等待Pulsar\n\
until curl -f http://pulsar:8080/admin/v2/clusters; do\n\
  echo "Waiting for Pulsar..."\n\
  sleep 5\n\
done\n\
\n\
echo "Starting Business Service..."\n\
exec java $JAVA_OPTS -jar app.jar' > /app/entrypoint.sh && \
    chmod +x /app/entrypoint.sh

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=90s --retries=3 \
    CMD curl -f http://localhost:${SERVER_PORT}/actuator/health || exit 1

# 暴露端口
EXPOSE ${SERVER_PORT}

# 启动应用
ENTRYPOINT ["/app/entrypoint.sh"]
