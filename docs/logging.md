# 日志配置使用指南

本文档详细介绍了MicroSystem微服务架构系统的日志配置和使用方法，包括异步日志、动态日志级别控制、日志降级等功能。

## 功能特性

### 1. 异步日志配置
- 使用Logback的AsyncAppender实现异步写日志
- 配置合适的队列大小和丢弃策略
- 确保高并发场景下的日志性能

### 2. 统一降级开关
- 在Nacos配置中心添加全局日志级别控制开关
- 支持动态调整日志级别，无需重启服务
- 提供统一的日志降级策略

### 3. 智能日志级别策略
- **正常模式**: 打印所有级别的日志（DEBUG、INFO、WARN、ERROR）
- **降级模式**: 仅打印关键日志（HTTP请求/响应、ERROR、业务关键操作）

### 4. 自动监控和降级
- 监控系统资源使用情况（CPU、内存、QPS、错误率）
- 自动触发日志降级，保护系统性能
- 资源恢复后自动取消降级

## 配置说明

### 1. Nacos配置中心

#### 日志配置文件：`logging-config.yml`

```yaml
logging:
  # 日志降级配置
  degradation:
    enabled: false          # 是否启用降级
    level: WARN            # 降级级别
    triggers:
      cpu-threshold: 80     # CPU阈值(%)
      memory-threshold: 85  # 内存阈值(%)
      qps-threshold: 1000   # QPS阈值
      error-rate-threshold: 5 # 错误率阈值(%)
  
  # 正常模式配置
  normal:
    level: INFO
  
  # HTTP请求日志
  http:
    enabled: true
    log-request-body: false
    log-response-body: false
  
  # 性能监控
  performance:
    enabled: true
    slow-method-threshold: 1000
    very-slow-method-threshold: 5000
  
  # 异步日志配置
  async:
    queue-size: 2048
    discarding-threshold: 0
    max-flush-time: 2000
    never-block: true
```

### 2. 服务级别配置

每个服务都有独立的logback-spring.xml配置文件：

- `auth-service/src/main/resources/logback-spring.xml`
- `business-service/src/main/resources/logback-spring.xml`
- `job-service/src/main/resources/logback-spring.xml`
- `gateway-service/src/main/resources/logback-spring.xml`

## 使用方法

### 1. 动态调整日志级别

#### 通过API接口

```bash
# 获取日志状态
curl http://localhost:8080/api/log/status

# 启用日志降级
curl -X POST http://localhost:8080/api/log/degradation/enable

# 禁用日志降级
curl -X POST http://localhost:8080/api/log/degradation/disable

# 调整指定Logger级别
curl -X POST "http://localhost:8080/api/log/level?loggerName=com.microsystem.auth&level=DEBUG"

# 获取监控状态
curl http://localhost:8080/api/log/monitor/status

# 测试日志输出
curl -X POST http://localhost:8080/api/log/test
```

#### 通过Nacos配置中心

1. 登录Nacos控制台：http://localhost:8848/nacos
2. 进入配置管理 -> 配置列表
3. 编辑 `logging-config.yml` 配置
4. 修改 `logging.degradation.enabled` 为 `true` 或 `false`
5. 发布配置，系统会自动刷新

### 2. 业务代码中使用日志工具

#### 导入日志工具类

```java
import com.microsystem.common.log.LogUtils;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class UserService {
    
    public void login(String username, String password) {
        // 设置用户ID到MDC
        LogUtils.setUserId(username);
        
        try {
            // 记录业务关键操作
            LogUtils.logBusinessCritical("用户登录", 
                String.format("用户: %s, IP: %s", username, getClientIp()));
            
            // 普通业务日志
            log.info("用户登录验证开始: {}", username);
            
            // 验证逻辑...
            
        } catch (Exception e) {
            // 记录异常日志
            LogUtils.logException(log, "用户登录", e);
            throw e;
        } finally {
            // 清理MDC
            LogUtils.clearMDC();
        }
    }
}
```

#### 记录不同类型的日志

```java
// HTTP请求日志
LogUtils.logHttpRequest("POST", "/api/auth/login", "{username:test}", "127.0.0.1");

// HTTP响应日志
LogUtils.logHttpResponse("POST", "/api/auth/login", 200, 150);

// 业务关键操作日志
LogUtils.logBusinessCritical("产品发布", "产品ID: 123, 操作人: admin");

// 性能监控日志
LogUtils.logPerformance("数据库查询", 1200, "查询用户信息");

// 安全审计日志
LogUtils.logSecurity("登录失败", "用户名: test, IP: 127.0.0.1", "WARN");

// 数据库操作日志
LogUtils.logDatabaseOperation("SELECT", "users", "username=test", 50);

// 缓存操作日志
LogUtils.logCacheOperation("GET", "user:123", true, 5);

// 消息队列日志
LogUtils.logMessageQueue("SEND", "user-topic", "msg-123", "SUCCESS");
```

### 3. 链路追踪

系统自动为每个请求生成TraceId，用于链路追踪：

```java
// 生成并设置TraceId
String traceId = LogUtils.generateAndSetTraceId();

// 获取当前TraceId
String currentTraceId = LogUtils.getTraceId();

// 设置其他上下文信息
LogUtils.setUserId("user123");
LogUtils.setRequestId("req456");
LogUtils.setServiceName("auth-service");
LogUtils.setMethodName("login");
```

## 日志文件说明

### 1. 文件结构

每个服务的日志文件按类型分离：

```
logs/
├── auth-service/
│   ├── auth-service.log              # 应用日志
│   ├── auth-service-error.log        # 错误日志
│   ├── auth-service-http-request.log # HTTP请求日志
│   ├── auth-service-http-response.log# HTTP响应日志
│   ├── auth-service-business-critical.log # 业务关键操作日志
│   └── auth-service-security.log     # 安全审计日志
├── business-service/
│   ├── business-service.log
│   ├── business-service-error.log
│   ├── business-service-critical.log
│   ├── business-service-performance.log
│   ├── business-service-database.log
│   └── business-service-mq.log
└── ...
```

### 2. 日志格式

统一的日志格式包含链路追踪信息：

```
2024-12-03 10:30:45.123 [http-nio-8080-exec-1] INFO [abc123def456] [user789] com.microsystem.auth.service.UserService - 用户登录验证开始: testuser
```

格式说明：
- `2024-12-03 10:30:45.123`: 时间戳
- `[http-nio-8080-exec-1]`: 线程名
- `INFO`: 日志级别
- `[abc123def456]`: TraceId
- `[user789]`: 用户ID
- `com.microsystem.auth.service.UserService`: Logger名称
- `用户登录验证开始: testuser`: 日志消息

### 3. 文件滚动策略

- **大小滚动**: 单个文件最大100MB
- **时间滚动**: 按天滚动
- **压缩存储**: 历史文件自动压缩
- **保留策略**: 
  - 应用日志保留30天
  - 错误日志保留60天
  - 安全日志保留365天
  - HTTP日志保留7天

## 性能优化

### 1. 异步日志配置

所有日志都使用异步写入，配置参数：

```xml
<appender name="ASYNC_APP_FILE" class="ch.qos.logback.classic.AsyncAppender">
    <appender-ref ref="APP_FILE"/>
    <queueSize>2048</queueSize>           <!-- 队列大小 -->
    <discardingThreshold>0</discardingThreshold> <!-- 不丢弃日志 -->
    <maxFlushTime>2000</maxFlushTime>     <!-- 最大刷新时间 -->
    <neverBlock>true</neverBlock>         <!-- 永不阻塞 -->
</appender>
```

### 2. 日志级别优化

- **开发环境**: DEBUG级别，记录详细信息
- **测试环境**: INFO级别，记录关键信息
- **生产环境**: WARN级别，仅记录警告和错误

### 3. 自动降级机制

当系统资源紧张时自动降级：

- CPU使用率 > 80%
- 内存使用率 > 85%
- QPS > 1000
- 错误率 > 5%

## 监控和告警

### 1. 监控指标

- 日志写入速度
- 队列使用情况
- 文件大小和数量
- 系统资源使用率

### 2. 告警规则

- 错误日志频率过高
- 日志队列满载
- 磁盘空间不足
- 日志写入失败

### 3. 监控接口

```bash
# 获取日志状态
GET /api/log/status

# 获取监控状态
GET /api/log/monitor/status

# 获取所有日志级别
GET /api/log/levels
```

## 故障排查

### 1. 常见问题

#### 日志不输出
- 检查日志级别配置
- 确认Logger名称正确
- 验证文件权限

#### 性能问题
- 检查异步队列是否满载
- 调整队列大小
- 启用日志降级

#### 文件过大
- 检查滚动策略配置
- 调整文件大小限制
- 清理历史文件

### 2. 调试命令

```bash
# 查看日志文件
tail -f logs/auth-service/auth-service.log

# 搜索特定TraceId的日志
grep "abc123def456" logs/*/*.log

# 查看错误日志
tail -f logs/*/error.log

# 监控日志写入速度
watch -n 1 'ls -la logs/*/*.log'
```

## 最佳实践

### 1. 日志记录原则

- **关键业务操作必须记录**：登录、支付、订单等
- **错误信息详细记录**：包含异常堆栈和上下文
- **性能敏感操作记录耗时**：数据库查询、外部调用等
- **安全相关操作记录**：认证失败、权限检查等

### 2. 日志内容规范

- 使用结构化日志格式
- 包含必要的上下文信息
- 避免记录敏感信息（密码、身份证等）
- 使用统一的日志工具类

### 3. 性能考虑

- 生产环境避免DEBUG级别日志
- 使用异步日志减少性能影响
- 合理设置日志保留策略
- 监控日志对系统性能的影响

### 4. 运维建议

- 定期清理历史日志文件
- 监控磁盘空间使用情况
- 建立日志告警机制
- 定期备份重要日志文件
