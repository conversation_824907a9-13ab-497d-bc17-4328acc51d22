# API接口文档

## 概述

MicroSystem提供RESTful API接口，所有接口都通过API网关统一访问。本文档描述了主要的API接口规范和使用方法。

## 基础信息

- **Base URL**: `http://localhost:8080`
- **API版本**: v1
- **数据格式**: JSON
- **字符编码**: UTF-8

## 认证方式

系统使用JWT Token进行身份认证：

1. 通过登录接口获取访问令牌
2. 在请求头中携带令牌：`Authorization: Bearer {token}`
3. 令牌过期后需要使用刷新令牌获取新的访问令牌

## 通用响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": 1701234567890
}
```

### 响应状态码

| 状态码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |

## 认证服务 API

### 用户注册

**POST** `/api/auth/register`

**请求体**:
```json
{
  "username": "testuser",
  "password": "password123",
  "confirmPassword": "password123",
  "nickname": "测试用户",
  "email": "<EMAIL>",
  "mobile": "13800138000"
}
```

**响应**:
```json
{
  "code": 200,
  "message": "注册成功",
  "data": null
}
```

### 用户登录

**POST** `/api/auth/login`

**请求体**:
```json
{
  "username": "testuser",
  "password": "password123"
}
```

**响应**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenType": "Bearer",
    "expiresIn": 86400,
    "userInfo": {
      "id": 1,
      "username": "testuser",
      "nickname": "测试用户",
      "email": "<EMAIL>"
    }
  }
}
```

### 刷新令牌

**POST** `/api/auth/refresh`

**请求参数**:
- `refreshToken`: 刷新令牌

**响应**:
```json
{
  "code": 200,
  "message": "令牌刷新成功",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenType": "Bearer",
    "expiresIn": 86400
  }
}
```

### 用户登出

**POST** `/api/auth/logout`

**请求头**:
- `Authorization: Bearer {token}`

**响应**:
```json
{
  "code": 200,
  "message": "登出成功",
  "data": null
}
```

## 业务服务 API

### 创建产品

**POST** `/api/products`

**请求头**:
- `Authorization: Bearer {token}`

**请求体**:
```json
{
  "name": "iPhone 15 Pro",
  "description": "苹果最新旗舰手机",
  "price": 8999.00,
  "categoryId": 1,
  "categoryName": "手机",
  "stock": 100,
  "images": [
    "https://example.com/image1.jpg",
    "https://example.com/image2.jpg"
  ],
  "attributes": [
    {
      "name": "颜色",
      "value": "深空黑色"
    },
    {
      "name": "存储容量",
      "value": "256GB"
    }
  ]
}
```

**响应**:
```json
{
  "code": 200,
  "message": "产品创建成功",
  "data": {
    "id": 1,
    "name": "iPhone 15 Pro",
    "description": "苹果最新旗舰手机",
    "price": 8999.00,
    "currency": "CNY",
    "status": "DRAFT",
    "statusDesc": "草稿",
    "categoryId": 1,
    "categoryName": "手机",
    "stock": 100,
    "images": ["https://example.com/image1.jpg"],
    "attributes": {
      "颜色": "深空黑色",
      "存储容量": "256GB"
    },
    "recommendationScore": 50.0,
    "createdAt": "2024-12-03T10:00:00",
    "updatedAt": "2024-12-03T10:00:00"
  }
}
```

### 获取产品详情

**GET** `/api/products/{id}`

**路径参数**:
- `id`: 产品ID

**响应**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "name": "iPhone 15 Pro",
    "description": "苹果最新旗舰手机",
    "price": 8999.00,
    "currency": "CNY",
    "status": "PUBLISHED",
    "statusDesc": "已发布",
    "categoryId": 1,
    "categoryName": "手机",
    "stock": 100,
    "recommendationScore": 95.5
  }
}
```

### 分页查询产品

**GET** `/api/products`

**查询参数**:
- `page`: 页码（默认1）
- `size`: 每页大小（默认10）
- `name`: 产品名称（模糊查询）
- `status`: 产品状态
- `categoryId`: 分类ID
- `minPrice`: 最小价格
- `maxPrice`: 最大价格
- `sortBy`: 排序字段（默认createdAt）
- `sortDirection`: 排序方向（默认desc）

**响应**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "page": 1,
    "size": 10,
    "total": 100,
    "records": [
      {
        "id": 1,
        "name": "iPhone 15 Pro",
        "price": 8999.00,
        "status": "PUBLISHED"
      }
    ]
  }
}
```

### 发布产品

**POST** `/api/products/{id}/publish`

**路径参数**:
- `id`: 产品ID

**响应**:
```json
{
  "code": 200,
  "message": "产品发布成功",
  "data": null
}
```

### 下架产品

**POST** `/api/products/{id}/unpublish`

**路径参数**:
- `id`: 产品ID

**响应**:
```json
{
  "code": 200,
  "message": "产品下架成功",
  "data": null
}
```

## 任务服务 API

### 创建任务

**POST** `/api/jobs`

**请求头**:
- `Authorization: Bearer {token}`

**请求体**:
```json
{
  "jobName": "数据同步任务",
  "jobDescription": "定时同步用户数据",
  "processorInfo": "com.microsystem.job.processor.DataSyncJobProcessor",
  "cronExpression": "0 0 2 * * ?",
  "jobParams": "{\"source\":\"mysql\",\"target\":\"elasticsearch\"}",
  "maxInstanceNum": 1,
  "concurrency": 5,
  "instanceTimeLimit": 300000,
  "instanceRetryNum": 3
}
```

**响应**:
```json
{
  "code": 200,
  "message": "任务创建成功",
  "data": {
    "jobId": 1001,
    "jobName": "数据同步任务",
    "status": "ENABLE",
    "createTime": 1701234567890
  }
}
```

### 立即执行任务

**POST** `/api/jobs/{jobId}/run`

**路径参数**:
- `jobId`: 任务ID

**查询参数**:
- `instanceParams`: 实例参数（可选）

**响应**:
```json
{
  "code": 200,
  "message": "任务执行成功",
  "data": 1001001
}
```

## 错误处理

### 错误响应格式

```json
{
  "code": 400,
  "message": "请求参数错误",
  "data": null,
  "timestamp": 1701234567890
}
```

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 40001 | 参数验证失败 | 检查请求参数格式和必填项 |
| 40101 | 未授权访问 | 检查Authorization头是否正确 |
| 40102 | Token已过期 | 使用刷新令牌获取新的访问令牌 |
| 40301 | 禁止访问 | 检查用户权限 |
| 40401 | 资源不存在 | 确认资源ID是否正确 |
| 40901 | 资源冲突 | 检查是否存在重复数据 |
| 42901 | 请求过于频繁 | 降低请求频率 |

## 限流规则

系统实施了API限流保护：

- **登录接口**: 每分钟最多10次
- **注册接口**: 每分钟最多5次
- **产品查询**: 每分钟最多100次
- **任务操作**: 每分钟最多50次

超出限制将返回429状态码。

## SDK和示例

### cURL示例

```bash
# 用户登录
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","password":"password123"}'

# 创建产品
curl -X POST http://localhost:8080/api/products \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{"name":"测试产品","price":99.99,"categoryId":1,"categoryName":"测试分类","stock":100}'
```

### JavaScript示例

```javascript
// 用户登录
const loginResponse = await fetch('http://localhost:8080/api/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    username: 'testuser',
    password: 'password123'
  })
});

const loginData = await loginResponse.json();
const token = loginData.data.accessToken;

// 获取产品列表
const productsResponse = await fetch('http://localhost:8080/api/products', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});

const productsData = await productsResponse.json();
```
