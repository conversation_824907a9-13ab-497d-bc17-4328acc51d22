# 部署指南

本文档详细介绍了MicroSystem微服务架构系统的部署方法，包括本地开发环境、测试环境和生产环境的部署步骤。

## 环境要求

### 基础环境

- **操作系统**: Linux (推荐 Ubuntu 20.04+, CentOS 8+)
- **Java**: OpenJDK 17+
- **Maven**: 3.8+
- **Docker**: 20.10+
- **Docker Compose**: 2.0+

### 硬件要求

#### 开发环境
- **CPU**: 4核心
- **内存**: 8GB
- **磁盘**: 50GB

#### 测试环境
- **CPU**: 8核心
- **内存**: 16GB
- **磁盘**: 100GB

#### 生产环境
- **CPU**: 16核心
- **内存**: 32GB
- **磁盘**: 500GB SSD

## 本地开发环境部署

### 1. 环境准备

```bash
# 安装Java 17
sudo apt update
sudo apt install openjdk-17-jdk

# 验证Java版本
java -version

# 安装Maven
sudo apt install maven

# 验证Maven版本
mvn -version

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. 克隆项目

```bash
git clone https://github.com/your-org/microsystem.git
cd microsystem
```

### 3. 启动基础设施

```bash
cd docker
cp .env.dev .env

# 启动基础设施服务
docker-compose up -d mysql redis nacos sentinel pulsar powerjob

# 等待服务启动完成
sleep 30

# 验证服务状态
docker-compose ps
```

### 4. 初始化数据库

```bash
# 执行数据库初始化脚本
mysql -h localhost -P 3306 -u root -p < ../scripts/database/init-databases.sql
mysql -h localhost -P 3306 -u root -p < ../scripts/database/init-data.sql
```

### 5. 构建和启动微服务

```bash
# 返回项目根目录
cd ..

# 构建项目
mvn clean package -DskipTests

# 启动微服务（推荐使用IDE启动以便调试）
# 或者使用命令行启动
cd auth-service && mvn spring-boot:run &
cd ../business-service && mvn spring-boot:run &
cd ../job-service && mvn spring-boot:run &
cd ../gateway-service && mvn spring-boot:run &
```

### 6. 验证部署

```bash
# 检查服务健康状态
curl http://localhost:8080/actuator/health
curl http://localhost:8081/actuator/health
curl http://localhost:8082/actuator/health
curl http://localhost:8083/actuator/health

# 测试API接口
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'
```

## Docker容器化部署

### 1. 一键部署

```bash
# 使用部署脚本一键部署
chmod +x scripts/docker/deploy.sh
./scripts/docker/deploy.sh
```

### 2. 手动部署

```bash
# 构建镜像
chmod +x scripts/docker/build-images.sh
./scripts/docker/build-images.sh

# 启动服务
cd docker
docker-compose up -d

# 查看服务状态
docker-compose ps
docker-compose logs -f
```

### 3. 环境配置

根据部署环境选择对应的配置文件：

```bash
# 开发环境
cp .env.dev .env

# 测试环境
cp .env.test .env

# 生产环境
cp .env.prod .env
```

## 生产环境部署

### 1. 服务器准备

#### 1.1 系统优化

```bash
# 调整系统参数
echo 'vm.max_map_count=262144' >> /etc/sysctl.conf
echo 'fs.file-max=65536' >> /etc/sysctl.conf
sysctl -p

# 调整ulimit
echo '* soft nofile 65536' >> /etc/security/limits.conf
echo '* hard nofile 65536' >> /etc/security/limits.conf
```

#### 1.2 防火墙配置

```bash
# 开放必要端口
ufw allow 8080  # Gateway
ufw allow 8081  # Auth Service
ufw allow 8082  # Business Service
ufw allow 8083  # Job Service
ufw allow 8848  # Nacos
ufw allow 3306  # MySQL
ufw allow 6379  # Redis
```

### 2. 数据库部署

#### 2.1 MySQL集群部署

```bash
# 使用Docker部署MySQL主从集群
docker run -d --name mysql-master \
  -e MYSQL_ROOT_PASSWORD=your_password \
  -e MYSQL_REPLICATION_MODE=master \
  -e MYSQL_REPLICATION_USER=replicator \
  -e MYSQL_REPLICATION_PASSWORD=replicator_password \
  -p 3306:3306 \
  mysql:8.0

docker run -d --name mysql-slave \
  -e MYSQL_ROOT_PASSWORD=your_password \
  -e MYSQL_REPLICATION_MODE=slave \
  -e MYSQL_REPLICATION_USER=replicator \
  -e MYSQL_REPLICATION_PASSWORD=replicator_password \
  -e MYSQL_MASTER_HOST=mysql-master \
  -p 3307:3306 \
  mysql:8.0
```

#### 2.2 Redis集群部署

```bash
# 使用脚本部署Redis集群
chmod +x scripts/deploy/setup-databases.sh
./scripts/deploy/setup-databases.sh
```

### 3. 中间件部署

```bash
# 部署Nacos、Sentinel、Pulsar等中间件
chmod +x scripts/deploy/setup-middleware.sh
./scripts/deploy/setup-middleware.sh
```

### 4. 微服务部署

#### 4.1 使用Docker Swarm

```bash
# 初始化Swarm集群
docker swarm init

# 部署服务栈
docker stack deploy -c docker-compose.prod.yml microsystem
```

#### 4.2 使用Kubernetes

```bash
# 应用Kubernetes配置
kubectl apply -f k8s/namespace.yaml
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/secret.yaml
kubectl apply -f k8s/deployment.yaml
kubectl apply -f k8s/service.yaml
kubectl apply -f k8s/ingress.yaml
```

### 5. 监控和日志

#### 5.1 部署监控系统

```bash
# 部署Prometheus和Grafana
docker-compose -f docker-compose.monitoring.yml up -d
```

#### 5.2 配置日志收集

```bash
# 部署ELK Stack
docker-compose -f docker-compose.logging.yml up -d
```

## 配置管理

### 1. Nacos配置

访问Nacos控制台 (http://localhost:8848/nacos)，配置以下内容：

- **common-config.yml**: 公共配置
- **auth-service.yml**: 认证服务配置
- **business-service.yml**: 业务服务配置
- **job-service.yml**: 任务服务配置
- **gateway-service.yml**: 网关服务配置

### 2. 环境变量

生产环境需要设置以下环境变量：

```bash
export MYSQL_ROOT_PASSWORD=your_secure_password
export MYSQL_PASSWORD=your_secure_password
export REDIS_PASSWORD=your_secure_password
export NACOS_USERNAME=your_nacos_username
export NACOS_PASSWORD=your_nacos_password
export JWT_SECRET=your_jwt_secret_key
```

## 健康检查和监控

### 1. 健康检查端点

- Gateway: http://localhost:8080/actuator/health
- Auth Service: http://localhost:8081/actuator/health
- Business Service: http://localhost:8082/actuator/health
- Job Service: http://localhost:8083/actuator/health

### 2. 监控指标

- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3000
- **Nacos**: http://localhost:8848/nacos
- **Sentinel**: http://localhost:8858

## 故障排查

### 1. 常见问题

#### 服务启动失败
```bash
# 查看服务日志
docker-compose logs service-name

# 检查端口占用
netstat -tlnp | grep port-number

# 检查磁盘空间
df -h
```

#### 数据库连接失败
```bash
# 测试数据库连接
mysql -h host -P port -u username -p

# 检查数据库状态
docker exec mysql-container mysqladmin status
```

#### 服务注册失败
```bash
# 检查Nacos状态
curl http://nacos-host:8848/nacos/v1/ns/operator/metrics

# 查看服务注册信息
curl http://nacos-host:8848/nacos/v1/ns/instance/list?serviceName=service-name
```

### 2. 性能调优

#### JVM参数调优
```bash
# 生产环境JVM参数示例
JAVA_OPTS="-Xms4g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+HeapDumpOnOutOfMemoryError"
```

#### 数据库调优
```sql
-- MySQL配置优化
SET GLOBAL innodb_buffer_pool_size = 2147483648;
SET GLOBAL max_connections = 1000;
SET GLOBAL query_cache_size = 268435456;
```

## 备份和恢复

### 1. 数据库备份

```bash
# 自动备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -h localhost -u root -p --all-databases > backup_$DATE.sql
```

### 2. 配置备份

```bash
# 备份Nacos配置
curl -X GET "http://nacos:8848/nacos/v1/cs/configs?export=true&group=DEFAULT_GROUP" > nacos_config_backup.zip
```

## 扩容和缩容

### 1. 水平扩容

```bash
# Docker Compose扩容
docker-compose up -d --scale business-service=3

# Kubernetes扩容
kubectl scale deployment business-service --replicas=3
```

### 2. 垂直扩容

```bash
# 调整容器资源限制
docker update --memory=4g --cpus=2 container-name
```

## 安全配置

### 1. SSL/TLS配置

```bash
# 生成SSL证书
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout private.key -out certificate.crt
```

### 2. 访问控制

```bash
# 配置防火墙规则
iptables -A INPUT -p tcp --dport 8080 -s trusted-ip -j ACCEPT
iptables -A INPUT -p tcp --dport 8080 -j DROP
```

## 升级和回滚

### 1. 滚动升级

```bash
# 逐个升级服务
docker-compose up -d --no-deps service-name
```

### 2. 快速回滚

```bash
# 回滚到上一个版本
docker-compose down
docker-compose up -d
```
