package com.microsystem.gateway.filter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 限流过滤器
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class RateLimitFilter implements GlobalFilter, Ordered {

    @Autowired
    private ReactiveRedisTemplate<String, String> redisTemplate;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 限流配置
     */
    private static final int DEFAULT_RATE_LIMIT = 100; // 默认每分钟100次
    private static final Duration WINDOW_SIZE = Duration.ofMinutes(1); // 时间窗口1分钟

    /**
     * Lua脚本实现滑动窗口限流
     */
    private static final String RATE_LIMIT_SCRIPT = 
        "local key = KEYS[1] " +
        "local window = tonumber(ARGV[1]) " +
        "local limit = tonumber(ARGV[2]) " +
        "local current = redis.call('incr', key) " +
        "if current == 1 then " +
        "    redis.call('expire', key, window) " +
        "end " +
        "return current";

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        
        // 获取客户端IP
        String clientIp = getClientIp(request);
        String path = request.getURI().getPath();
        
        // 构建限流Key
        String rateLimitKey = buildRateLimitKey(clientIp, path);
        
        log.debug("限流检查: IP={}, Path={}, Key={}", clientIp, path, rateLimitKey);
        
        // 执行限流检查
        return checkRateLimit(rateLimitKey)
                .flatMap(allowed -> {
                    if (allowed) {
                        log.debug("限流检查通过: {}", rateLimitKey);
                        return chain.filter(exchange);
                    } else {
                        log.warn("触发限流: {}", rateLimitKey);
                        return handleRateLimitExceeded(exchange);
                    }
                })
                .onErrorResume(throwable -> {
                    log.error("限流检查异常: {}", throwable.getMessage(), throwable);
                    // 异常时允许通过，避免影响正常请求
                    return chain.filter(exchange);
                });
    }

    /**
     * 检查限流
     */
    private Mono<Boolean> checkRateLimit(String key) {
        RedisScript<Long> script = RedisScript.of(RATE_LIMIT_SCRIPT, Long.class);
        List<String> keys = Arrays.asList(key);
        List<String> args = Arrays.asList(
                String.valueOf(WINDOW_SIZE.getSeconds()),
                String.valueOf(DEFAULT_RATE_LIMIT)
        );
        
        return redisTemplate.execute(script, keys, args)
                .map(count -> count <= DEFAULT_RATE_LIMIT)
                .defaultIfEmpty(true); // 默认允许通过
    }

    /**
     * 构建限流Key
     */
    private String buildRateLimitKey(String clientIp, String path) {
        return String.format("rate_limit:%s:%s", clientIp, path.replaceAll("/", "_"));
    }

    /**
     * 获取客户端IP
     */
    private String getClientIp(ServerHttpRequest request) {
        String xForwardedFor = request.getHeaders().getFirst("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeaders().getFirst("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddress() != null ? 
                request.getRemoteAddress().getAddress().getHostAddress() : "unknown";
    }

    /**
     * 处理限流超出响应
     */
    private Mono<Void> handleRateLimitExceeded(ServerWebExchange exchange) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.TOO_MANY_REQUESTS);
        response.getHeaders().add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        
        Map<String, Object> result = new HashMap<>();
        result.put("code", HttpStatus.TOO_MANY_REQUESTS.value());
        result.put("message", "请求过于频繁，请稍后重试");
        result.put("data", null);
        result.put("timestamp", System.currentTimeMillis());
        
        try {
            String body = objectMapper.writeValueAsString(result);
            DataBuffer buffer = response.bufferFactory().wrap(body.getBytes(StandardCharsets.UTF_8));
            return response.writeWith(Mono.just(buffer));
        } catch (JsonProcessingException e) {
            log.error("序列化响应失败: {}", e.getMessage(), e);
            return response.setComplete();
        }
    }

    @Override
    public int getOrder() {
        return -50; // 在认证过滤器之后执行
    }
}
