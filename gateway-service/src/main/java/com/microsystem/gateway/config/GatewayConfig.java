package com.microsystem.gateway.config;

import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.reactive.CorsWebFilter;
import org.springframework.web.cors.reactive.UrlBasedCorsConfigurationSource;

/**
 * 网关配置类
 * 
 * <AUTHOR>
 */
@Configuration
public class GatewayConfig {

    /**
     * 路由配置
     */
    @Bean
    public RouteLocator customRouteLocator(RouteLocatorBuilder builder) {
        return builder.routes()
                // 认证服务路由
                .route("auth-service", r -> r
                        .path("/api/auth/**")
                        .uri("lb://auth-service"))
                
                // 业务服务路由
                .route("business-service", r -> r
                        .path("/api/products/**", "/api/business/**")
                        .uri("lb://business-service"))
                
                // 任务服务路由
                .route("job-service", r -> r
                        .path("/api/jobs/**")
                        .uri("lb://job-service"))
                
                // Actuator路由
                .route("actuator", r -> r
                        .path("/actuator/**")
                        .filters(f -> f.stripPrefix(0))
                        .uri("lb://gateway-service"))
                
                .build();
    }

    /**
     * CORS配置
     */
    @Bean
    public CorsWebFilter corsWebFilter() {
        CorsConfiguration corsConfig = new CorsConfiguration();
        
        // 允许的源
        corsConfig.addAllowedOriginPattern("*");
        
        // 允许的请求头
        corsConfig.addAllowedHeader("*");
        
        // 允许的请求方法
        corsConfig.addAllowedMethod("*");
        
        // 允许携带凭证
        corsConfig.setAllowCredentials(true);
        
        // 预检请求的缓存时间
        corsConfig.setMaxAge(3600L);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", corsConfig);
        
        return new CorsWebFilter(source);
    }
}
