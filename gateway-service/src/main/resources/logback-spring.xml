<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds">
    
    <!-- 引入Spring Boot默认配置 -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    
    <!-- 定义日志文件路径 -->
    <springProfile name="!docker">
        <property name="LOG_PATH" value="./logs/gateway-service"/>
    </springProfile>
    <springProfile name="docker">
        <property name="LOG_PATH" value="/app/logs"/>
    </springProfile>
    
    <!-- 定义日志格式 -->
    <property name="LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-}] [%X{requestId:-}] %logger{50} - %msg%n"/>
    
    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
    </appender>
    
    <!-- 异步控制台输出 -->
    <appender name="ASYNC_CONSOLE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="CONSOLE"/>
        <queueSize>1024</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <maxFlushTime>2000</maxFlushTime>
        <neverBlock>true</neverBlock>
    </appender>
    
    <!-- 应用日志文件 -->
    <appender name="APP_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/gateway-service.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/gateway-service.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    
    <!-- 异步应用日志 -->
    <appender name="ASYNC_APP_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="APP_FILE"/>
        <queueSize>2048</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <maxFlushTime>2000</maxFlushTime>
        <neverBlock>true</neverBlock>
    </appender>
    
    <!-- 错误日志文件 -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/gateway-service-error.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/gateway-service-error.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>60</maxHistory>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    
    <!-- 异步错误日志 -->
    <appender name="ASYNC_ERROR_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="ERROR_FILE"/>
        <queueSize>1024</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <maxFlushTime>2000</maxFlushTime>
        <neverBlock>true</neverBlock>
    </appender>
    
    <!-- 访问日志文件 -->
    <appender name="ACCESS_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/gateway-service-access.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/gateway-service-access.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>500MB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- 异步访问日志 -->
    <appender name="ASYNC_ACCESS_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="ACCESS_FILE"/>
        <queueSize>8192</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <maxFlushTime>500</maxFlushTime>
        <neverBlock>true</neverBlock>
    </appender>
    
    <!-- 限流日志文件 -->
    <appender name="RATE_LIMIT_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/gateway-service-rate-limit.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/gateway-service-rate-limit.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- 异步限流日志 -->
    <appender name="ASYNC_RATE_LIMIT_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="RATE_LIMIT_FILE"/>
        <queueSize>2048</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <maxFlushTime>1000</maxFlushTime>
        <neverBlock>true</neverBlock>
    </appender>
    
    <!-- 认证日志文件 -->
    <appender name="AUTH_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/gateway-service-auth.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/gateway-service-auth.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>90</maxHistory>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- 异步认证日志 -->
    <appender name="ASYNC_AUTH_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="AUTH_FILE"/>
        <queueSize>2048</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <maxFlushTime>1000</maxFlushTime>
        <neverBlock>true</neverBlock>
    </appender>
    
    <!-- 路由日志文件 -->
    <appender name="ROUTE_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/gateway-service-route.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/gateway-service-route.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- 异步路由日志 -->
    <appender name="ASYNC_ROUTE_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="ROUTE_FILE"/>
        <queueSize>2048</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <maxFlushTime>1000</maxFlushTime>
        <neverBlock>true</neverBlock>
    </appender>
    
    <!-- 特定Logger配置 -->
    <logger name="HTTP_REQUEST" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_ACCESS_FILE"/>
    </logger>
    
    <logger name="HTTP_RESPONSE" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_ACCESS_FILE"/>
    </logger>
    
    <logger name="RATE_LIMIT" level="WARN" additivity="false">
        <appender-ref ref="ASYNC_RATE_LIMIT_FILE"/>
    </logger>
    
    <logger name="GATEWAY_AUTH" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_AUTH_FILE"/>
    </logger>
    
    <logger name="GATEWAY_ROUTE" level="DEBUG" additivity="false">
        <appender-ref ref="ASYNC_ROUTE_FILE"/>
    </logger>
    
    <!-- 应用包日志级别 -->
    <logger name="com.microsystem.gateway" level="INFO"/>
    <logger name="com.microsystem.common" level="INFO"/>
    
    <!-- Spring Cloud Gateway日志 -->
    <logger name="org.springframework.cloud.gateway" level="INFO"/>
    <logger name="org.springframework.web.reactive" level="WARN"/>
    <logger name="reactor.netty" level="WARN"/>
    
    <!-- 第三方库日志级别 -->
    <logger name="org.springframework" level="WARN"/>
    <logger name="org.apache" level="WARN"/>
    <logger name="com.alibaba" level="WARN"/>
    <logger name="io.lettuce" level="WARN"/>
    
    <!-- 根Logger配置 -->
    <springProfile name="dev,test">
        <root level="DEBUG">
            <appender-ref ref="ASYNC_CONSOLE"/>
            <appender-ref ref="ASYNC_APP_FILE"/>
            <appender-ref ref="ASYNC_ERROR_FILE"/>
        </root>
    </springProfile>
    
    <springProfile name="prod,docker">
        <root level="INFO">
            <appender-ref ref="ASYNC_APP_FILE"/>
            <appender-ref ref="ASYNC_ERROR_FILE"/>
        </root>
    </springProfile>
    
</configuration>
