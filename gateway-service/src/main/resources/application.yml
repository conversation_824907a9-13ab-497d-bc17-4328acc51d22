server:
  port: 8080

spring:
  application:
    name: gateway-service
  profiles:
    active: dev

  # Cloud Gateway配置
  cloud:
    gateway:
      # 全局CORS配置
      globalcors:
        cors-configurations:
          '[/**]':
            allowed-origin-patterns: "*"
            allowed-methods: "*"
            allowed-headers: "*"
            allow-credentials: true
            max-age: 3600
      
      # 路由配置
      routes:
        # 认证服务
        - id: auth-service
          uri: lb://auth-service
          predicates:
            - Path=/api/auth/**
          filters:
            - StripPrefix=0
        
        # 业务服务
        - id: business-service
          uri: lb://business-service
          predicates:
            - Path=/api/products/**,/api/business/**
          filters:
            - StripPrefix=0
        
        # 任务服务
        - id: job-service
          uri: lb://job-service
          predicates:
            - Path=/api/jobs/**
          filters:
            - StripPrefix=0
      
      # 发现配置
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
    
    # Nacos配置
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: dev
        group: DEFAULT_GROUP
      config:
        server-addr: localhost:8848
        namespace: dev
        group: DEFAULT_GROUP
        file-extension: yml
        shared-configs:
          - data-id: common-config.yml
            group: DEFAULT_GROUP
            refresh: true
    
    # Sentinel配置
    sentinel:
      transport:
        dashboard: localhost:8080
        port: 8719
      scg:
        fallback:
          mode: response
          response-status: 429
          response-body: '{"code":429,"message":"服务暂时不可用，请稍后重试"}'

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password: 
      database: 3
      timeout: 10000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0

# JWT配置
jwt:
  secret: microsystem-jwt-secret-key-2024
  issuer: microsystem

# 日志配置
logging:
  level:
    com.microsystem: DEBUG
    org.springframework.cloud.gateway: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"

# Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,gateway
  endpoint:
    health:
      show-details: always
    gateway:
      enabled: true

# 网关配置
gateway:
  # 限流配置
  rate-limit:
    # 默认限流次数（每分钟）
    default-limit: 100
    # 时间窗口（秒）
    window-size: 60
  
  # 白名单配置
  white-list:
    - /api/auth/login
    - /api/auth/register
    - /api/auth/refresh
    - /api/auth/captcha
    - /actuator/**
    - /swagger-ui/**
    - /v3/api-docs/**
    - /webjars/**
    - /favicon.ico
