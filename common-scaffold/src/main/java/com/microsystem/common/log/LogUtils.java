package com.microsystem.common.log;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import java.util.UUID;

/**
 * 统一日志工具类
 * 提供统一的日志记录方法，支持链路追踪和业务关键操作标记
 * 
 * <AUTHOR>
 */
public class LogUtils {

    /**
     * HTTP请求日志Logger
     */
    public static final Logger HTTP_REQUEST_LOGGER = LoggerFactory.getLogger("HTTP_REQUEST");

    /**
     * HTTP响应日志Logger
     */
    public static final Logger HTTP_RESPONSE_LOGGER = LoggerFactory.getLogger("HTTP_RESPONSE");

    /**
     * 业务关键操作日志Logger
     */
    public static final Logger BUSINESS_CRITICAL_LOGGER = LoggerFactory.getLogger("BUSINESS_CRITICAL");

    /**
     * 性能监控日志Logger
     */
    public static final Logger PERFORMANCE_LOGGER = LoggerFactory.getLogger("PERFORMANCE");

    /**
     * 安全审计日志Logger
     */
    public static final Logger SECURITY_LOGGER = LoggerFactory.getLogger("SECURITY");

    /**
     * MDC键名常量
     */
    public static final String TRACE_ID = "traceId";
    public static final String USER_ID = "userId";
    public static final String REQUEST_ID = "requestId";
    public static final String SERVICE_NAME = "serviceName";
    public static final String METHOD_NAME = "methodName";

    /**
     * 生成并设置TraceId
     */
    public static String generateAndSetTraceId() {
        String traceId = generateTraceId();
        setTraceId(traceId);
        return traceId;
    }

    /**
     * 生成TraceId
     */
    public static String generateTraceId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 设置TraceId到MDC
     */
    public static void setTraceId(String traceId) {
        MDC.put(TRACE_ID, traceId);
    }

    /**
     * 获取当前TraceId
     */
    public static String getTraceId() {
        return MDC.get(TRACE_ID);
    }

    /**
     * 设置用户ID到MDC
     */
    public static void setUserId(String userId) {
        MDC.put(USER_ID, userId);
    }

    /**
     * 设置请求ID到MDC
     */
    public static void setRequestId(String requestId) {
        MDC.put(REQUEST_ID, requestId);
    }

    /**
     * 设置服务名称到MDC
     */
    public static void setServiceName(String serviceName) {
        MDC.put(SERVICE_NAME, serviceName);
    }

    /**
     * 设置方法名称到MDC
     */
    public static void setMethodName(String methodName) {
        MDC.put(METHOD_NAME, methodName);
    }

    /**
     * 清理MDC
     */
    public static void clearMDC() {
        MDC.clear();
    }

    /**
     * 记录HTTP请求日志
     */
    public static void logHttpRequest(String method, String uri, String params, String clientIp) {
        HTTP_REQUEST_LOGGER.info("HTTP请求 - Method: {}, URI: {}, Params: {}, ClientIP: {}", 
                method, uri, params, clientIp);
    }

    /**
     * 记录HTTP响应日志
     */
    public static void logHttpResponse(String method, String uri, int status, long duration) {
        HTTP_RESPONSE_LOGGER.info("HTTP响应 - Method: {}, URI: {}, Status: {}, Duration: {}ms", 
                method, uri, status, duration);
    }

    /**
     * 记录业务关键操作日志
     */
    public static void logBusinessCritical(String operation, String details) {
        BUSINESS_CRITICAL_LOGGER.info("关键业务操作 - Operation: {}, Details: {}", operation, details);
    }

    /**
     * 记录用户登录日志
     */
    public static void logUserLogin(String username, String clientIp, boolean success) {
        String result = success ? "成功" : "失败";
        BUSINESS_CRITICAL_LOGGER.info("用户登录 - Username: {}, ClientIP: {}, Result: {}", 
                username, clientIp, result);
        
        if (!success) {
            SECURITY_LOGGER.warn("登录失败 - Username: {}, ClientIP: {}", username, clientIp);
        }
    }

    /**
     * 记录用户注册日志
     */
    public static void logUserRegister(String username, String email, String clientIp) {
        BUSINESS_CRITICAL_LOGGER.info("用户注册 - Username: {}, Email: {}, ClientIP: {}", 
                username, email, clientIp);
    }

    /**
     * 记录产品操作日志
     */
    public static void logProductOperation(String operation, Long productId, String productName, String operator) {
        BUSINESS_CRITICAL_LOGGER.info("产品操作 - Operation: {}, ProductId: {}, ProductName: {}, Operator: {}", 
                operation, productId, productName, operator);
    }

    /**
     * 记录任务执行日志
     */
    public static void logJobExecution(String jobName, Long instanceId, String status, long duration) {
        BUSINESS_CRITICAL_LOGGER.info("任务执行 - JobName: {}, InstanceId: {}, Status: {}, Duration: {}ms", 
                jobName, instanceId, status, duration);
    }

    /**
     * 记录性能监控日志
     */
    public static void logPerformance(String operation, long duration, String details) {
        PERFORMANCE_LOGGER.info("性能监控 - Operation: {}, Duration: {}ms, Details: {}", 
                operation, duration, details);
    }

    /**
     * 记录安全审计日志
     */
    public static void logSecurity(String event, String details, String level) {
        switch (level.toUpperCase()) {
            case "WARN":
                SECURITY_LOGGER.warn("安全事件 - Event: {}, Details: {}", event, details);
                break;
            case "ERROR":
                SECURITY_LOGGER.error("安全事件 - Event: {}, Details: {}", event, details);
                break;
            default:
                SECURITY_LOGGER.info("安全事件 - Event: {}, Details: {}", event, details);
        }
    }

    /**
     * 记录数据库操作日志
     */
    public static void logDatabaseOperation(String operation, String table, String condition, long duration) {
        Logger logger = LoggerFactory.getLogger("DATABASE");
        logger.debug("数据库操作 - Operation: {}, Table: {}, Condition: {}, Duration: {}ms", 
                operation, table, condition, duration);
    }

    /**
     * 记录缓存操作日志
     */
    public static void logCacheOperation(String operation, String key, boolean hit, long duration) {
        Logger logger = LoggerFactory.getLogger("CACHE");
        logger.debug("缓存操作 - Operation: {}, Key: {}, Hit: {}, Duration: {}ms", 
                operation, key, hit, duration);
    }

    /**
     * 记录消息队列操作日志
     */
    public static void logMessageQueue(String operation, String topic, String messageId, String status) {
        Logger logger = LoggerFactory.getLogger("MESSAGE_QUEUE");
        logger.info("消息队列 - Operation: {}, Topic: {}, MessageId: {}, Status: {}", 
                operation, topic, messageId, status);
    }

    /**
     * 记录异常日志
     */
    public static void logException(Logger logger, String operation, Exception e) {
        logger.error("操作异常 - Operation: {}, Exception: {}, Message: {}", 
                operation, e.getClass().getSimpleName(), e.getMessage(), e);
    }

    /**
     * 记录方法执行时间
     */
    public static void logMethodExecution(Logger logger, String methodName, long duration) {
        if (duration > 1000) {
            logger.warn("方法执行耗时较长 - Method: {}, Duration: {}ms", methodName, duration);
        } else {
            logger.debug("方法执行 - Method: {}, Duration: {}ms", methodName, duration);
        }
    }

    /**
     * 创建带有上下文信息的日志消息
     */
    public static String createContextMessage(String message) {
        StringBuilder sb = new StringBuilder();
        sb.append(message);
        
        String traceId = getTraceId();
        if (traceId != null) {
            sb.append(" [TraceId: ").append(traceId).append("]");
        }
        
        String userId = MDC.get(USER_ID);
        if (userId != null) {
            sb.append(" [UserId: ").append(userId).append("]");
        }
        
        return sb.toString();
    }
}
