package com.microsystem.common.log;

import com.microsystem.common.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 日志管理控制器
 * 提供动态调整日志级别的API接口
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/log")
@RequiredArgsConstructor
@Tag(name = "日志管理", description = "动态日志级别管理接口")
public class LogManagementController {

    private final LogLevelManager logLevelManager;
    private final LogMonitor logMonitor;

    /**
     * 获取日志状态信息
     */
    @GetMapping("/status")
    @Operation(summary = "获取日志状态", description = "获取当前日志级别和降级状态信息")
    public Result<LogLevelManager.LogLevelStatus> getLogStatus() {
        LogLevelManager.LogLevelStatus status = logLevelManager.getStatus();
        return Result.success(status);
    }

    /**
     * 启用日志降级模式
     */
    @PostMapping("/degradation/enable")
    @Operation(summary = "启用日志降级", description = "启用日志降级模式，减少日志输出")
    public Result<String> enableDegradation() {
        logLevelManager.enableDegradation();
        LogUtils.logBusinessCritical("日志降级", "手动启用日志降级模式");
        return Result.success("日志降级模式已启用");
    }

    /**
     * 禁用日志降级模式
     */
    @PostMapping("/degradation/disable")
    @Operation(summary = "禁用日志降级", description = "禁用日志降级模式，恢复正常日志输出")
    public Result<String> disableDegradation() {
        logLevelManager.disableDegradation();
        LogUtils.logBusinessCritical("日志降级", "手动禁用日志降级模式");
        return Result.success("日志降级模式已禁用");
    }

    /**
     * 动态调整指定Logger的日志级别
     */
    @PostMapping("/level")
    @Operation(summary = "调整日志级别", description = "动态调整指定Logger的日志级别")
    public Result<String> adjustLogLevel(
            @Parameter(description = "Logger名称") @RequestParam String loggerName,
            @Parameter(description = "日志级别") @RequestParam String level) {

        String oldLevel = logLevelManager.getCurrentLogLevel(loggerName);
        logLevelManager.adjustLogLevel(loggerName, level);

        LogUtils.logBusinessCritical("日志级别调整",
                String.format("Logger: %s, 从 %s 调整到 %s", loggerName, oldLevel, level));

        return Result.success("日志级别调整成功");
    }

    /**
     * 获取指定Logger的当前日志级别
     */
    @GetMapping("/level")
    @Operation(summary = "获取日志级别", description = "获取指定Logger的当前日志级别")
    public Result<Map<String, String>> getLogLevel(
            @Parameter(description = "Logger名称") @RequestParam String loggerName) {
        
        String currentLevel = logLevelManager.getCurrentLogLevel(loggerName);
        
        Map<String, String> result = new HashMap<>();
        result.put("loggerName", loggerName);
        result.put("currentLevel", currentLevel);
        
        return Result.success(result);
    }

    /**
     * 获取所有关键Logger的日志级别
     */
    @GetMapping("/levels")
    @Operation(summary = "获取所有日志级别", description = "获取所有关键Logger的当前日志级别")
    public Result<Map<String, String>> getAllLogLevels() {
        Map<String, String> levels = new HashMap<>();
        
        // 关键Logger列表
        String[] loggers = {
            "ROOT",
            "com.microsystem",
            "com.microsystem.auth",
            "com.microsystem.business",
            "com.microsystem.job",
            "com.microsystem.gateway",
            "HTTP_REQUEST",
            "HTTP_RESPONSE",
            "BUSINESS_CRITICAL",
            "PERFORMANCE",
            "SECURITY"
        };
        
        for (String logger : loggers) {
            levels.put(logger, logLevelManager.getCurrentLogLevel(logger));
        }
        
        return Result.success(levels);
    }

    /**
     * 重置所有日志级别到默认状态
     */
    @PostMapping("/reset")
    @Operation(summary = "重置日志级别", description = "重置所有日志级别到默认状态")
    public Result<String> resetLogLevels() {
        // 先禁用降级模式
        logLevelManager.disableDegradation();

        // 重置关键Logger到默认级别
        logLevelManager.adjustLogLevel("ROOT", "INFO");
        logLevelManager.adjustLogLevel("com.microsystem", "INFO");
        logLevelManager.adjustLogLevel("HTTP_REQUEST", "INFO");
        logLevelManager.adjustLogLevel("HTTP_RESPONSE", "INFO");
        logLevelManager.adjustLogLevel("BUSINESS_CRITICAL", "INFO");

        LogUtils.logBusinessCritical("日志级别重置", "所有日志级别已重置到默认状态");

        return Result.success("日志级别已重置");
    }

    /**
     * 获取监控状态
     */
    @GetMapping("/monitor/status")
    @Operation(summary = "获取监控状态", description = "获取日志监控和系统资源使用状态")
    public Result<LogMonitor.MonitorStatus> getMonitorStatus() {
        LogMonitor.MonitorStatus status = logMonitor.getMonitorStatus();
        return Result.success(status);
    }

    /**
     * 测试日志输出
     */
    @PostMapping("/test")
    @Operation(summary = "测试日志输出", description = "测试各个级别的日志输出")
    public Result<String> testLogOutput() {
        String traceId = LogUtils.generateAndSetTraceId();

        try {
            log.trace("这是TRACE级别的测试日志");
            log.debug("这是DEBUG级别的测试日志");
            log.info("这是INFO级别的测试日志");
            log.warn("这是WARN级别的测试日志");
            log.error("这是ERROR级别的测试日志");

            LogUtils.logHttpRequest("GET", "/api/log/test", "{}", "127.0.0.1");
            LogUtils.logHttpResponse("GET", "/api/log/test", 200, 100);
            LogUtils.logBusinessCritical("日志测试", "测试各级别日志输出");
            LogUtils.logPerformance("日志测试", 100, "测试性能日志");
            LogUtils.logSecurity("日志测试", "测试安全日志", "INFO");

            return Result.success("日志测试完成，TraceId: " + traceId);

        } finally {
            LogUtils.clearMDC();
        }
    }
}
