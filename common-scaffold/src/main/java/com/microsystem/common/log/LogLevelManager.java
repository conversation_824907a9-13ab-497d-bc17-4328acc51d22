package com.microsystem.common.log;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 日志级别管理器
 * 支持动态调整日志级别，实现日志降级功能
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@RefreshScope
public class LogLevelManager {

    /**
     * 日志降级开关
     */
    @Value("${logging.degradation.enabled:false}")
    private boolean degradationEnabled;

    /**
     * 降级模式下的日志级别
     */
    @Value("${logging.degradation.level:WARN}")
    private String degradationLevel;

    /**
     * 正常模式下的日志级别
     */
    @Value("${logging.normal.level:INFO}")
    private String normalLevel;

    /**
     * 业务关键操作的包路径
     */
    @Value("${logging.business.critical.packages:com.microsystem}")
    private String criticalPackages;

    /**
     * HTTP请求日志开关
     */
    @Value("${logging.http.enabled:true}")
    private boolean httpLogEnabled;

    /**
     * 当前是否处于降级模式
     */
    private final AtomicBoolean isDegraded = new AtomicBoolean(false);

    /**
     * 原始日志级别缓存
     */
    private final ConcurrentHashMap<String, Level> originalLevels = new ConcurrentHashMap<>();

    /**
     * 关键业务操作的Logger名称
     */
    private static final String[] CRITICAL_LOGGERS = {
        "com.microsystem.auth.service",
        "com.microsystem.business.application.service",
        "com.microsystem.job.service",
        "com.microsystem.gateway.filter",
        "HTTP_REQUEST",
        "HTTP_RESPONSE",
        "BUSINESS_CRITICAL"
    };

    @PostConstruct
    public void init() {
        log.info("初始化日志级别管理器，降级开关: {}, 降级级别: {}, 正常级别: {}", 
                degradationEnabled, degradationLevel, normalLevel);
        
        // 根据配置决定是否启用降级模式
        if (degradationEnabled) {
            enableDegradation();
        } else {
            disableDegradation();
        }
    }

    /**
     * 启用日志降级模式
     */
    public void enableDegradation() {
        if (isDegraded.compareAndSet(false, true)) {
            log.warn("启用日志降级模式，级别: {}", degradationLevel);
            
            LoggerContext context = (LoggerContext) LoggerFactory.getILoggerFactory();
            Level targetLevel = Level.toLevel(degradationLevel, Level.WARN);
            
            // 保存原始级别并设置降级级别
            for (Logger logger : context.getLoggerList()) {
                if (logger.getLevel() != null && !isCriticalLogger(logger.getName())) {
                    originalLevels.put(logger.getName(), logger.getLevel());
                    logger.setLevel(targetLevel);
                }
            }
            
            // 设置根Logger级别
            Logger rootLogger = context.getLogger(Logger.ROOT_LOGGER_NAME);
            if (rootLogger.getLevel() != null) {
                originalLevels.put(Logger.ROOT_LOGGER_NAME, rootLogger.getLevel());
                rootLogger.setLevel(targetLevel);
            }
            
            log.warn("日志降级模式已启用，当前级别: {}", targetLevel);
        }
    }

    /**
     * 禁用日志降级模式
     */
    public void disableDegradation() {
        if (isDegraded.compareAndSet(true, false)) {
            log.info("禁用日志降级模式，恢复正常日志级别");
            
            LoggerContext context = (LoggerContext) LoggerFactory.getILoggerFactory();
            
            // 恢复原始日志级别
            originalLevels.forEach((loggerName, originalLevel) -> {
                Logger logger = context.getLogger(loggerName);
                logger.setLevel(originalLevel);
            });
            
            originalLevels.clear();
            log.info("日志级别已恢复正常");
        }
    }

    /**
     * 动态调整日志级别
     */
    public void adjustLogLevel(String loggerName, String level) {
        LoggerContext context = (LoggerContext) LoggerFactory.getILoggerFactory();
        Logger logger = context.getLogger(loggerName);
        Level targetLevel = Level.toLevel(level, Level.INFO);
        
        log.info("调整Logger [{}] 级别从 [{}] 到 [{}]", 
                loggerName, logger.getLevel(), targetLevel);
        
        logger.setLevel(targetLevel);
    }

    /**
     * 获取当前日志级别
     */
    public String getCurrentLogLevel(String loggerName) {
        LoggerContext context = (LoggerContext) LoggerFactory.getILoggerFactory();
        Logger logger = context.getLogger(loggerName);
        return logger.getLevel() != null ? logger.getLevel().toString() : "INHERIT";
    }

    /**
     * 判断是否为关键业务Logger
     */
    private boolean isCriticalLogger(String loggerName) {
        if (loggerName == null) {
            return false;
        }
        
        for (String criticalLogger : CRITICAL_LOGGERS) {
            if (loggerName.startsWith(criticalLogger)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 检查是否应该记录日志
     */
    public boolean shouldLog(String loggerName, String level) {
        if (!isDegraded.get()) {
            return true;
        }
        
        // 降级模式下的日志策略
        Level logLevel = Level.toLevel(level, Level.INFO);
        Level degradeLevel = Level.toLevel(degradationLevel, Level.WARN);
        
        // ERROR级别始终记录
        if (logLevel.isGreaterOrEqual(Level.ERROR)) {
            return true;
        }
        
        // 关键业务Logger始终记录
        if (isCriticalLogger(loggerName)) {
            return true;
        }
        
        // HTTP日志根据配置决定
        if (httpLogEnabled && (loggerName.contains("HTTP_REQUEST") || loggerName.contains("HTTP_RESPONSE"))) {
            return true;
        }
        
        // 其他日志根据降级级别决定
        return logLevel.isGreaterOrEqual(degradeLevel);
    }

    /**
     * 获取当前状态信息
     */
    public LogLevelStatus getStatus() {
        return LogLevelStatus.builder()
                .degradationEnabled(degradationEnabled)
                .isDegraded(isDegraded.get())
                .degradationLevel(degradationLevel)
                .normalLevel(normalLevel)
                .httpLogEnabled(httpLogEnabled)
                .criticalPackages(criticalPackages)
                .build();
    }

    /**
     * 日志级别状态信息
     */
    public static class LogLevelStatus {
        private boolean degradationEnabled;
        private boolean isDegraded;
        private String degradationLevel;
        private String normalLevel;
        private boolean httpLogEnabled;
        private String criticalPackages;

        public static LogLevelStatusBuilder builder() {
            return new LogLevelStatusBuilder();
        }

        public static class LogLevelStatusBuilder {
            private boolean degradationEnabled;
            private boolean isDegraded;
            private String degradationLevel;
            private String normalLevel;
            private boolean httpLogEnabled;
            private String criticalPackages;

            public LogLevelStatusBuilder degradationEnabled(boolean degradationEnabled) {
                this.degradationEnabled = degradationEnabled;
                return this;
            }

            public LogLevelStatusBuilder isDegraded(boolean isDegraded) {
                this.isDegraded = isDegraded;
                return this;
            }

            public LogLevelStatusBuilder degradationLevel(String degradationLevel) {
                this.degradationLevel = degradationLevel;
                return this;
            }

            public LogLevelStatusBuilder normalLevel(String normalLevel) {
                this.normalLevel = normalLevel;
                return this;
            }

            public LogLevelStatusBuilder httpLogEnabled(boolean httpLogEnabled) {
                this.httpLogEnabled = httpLogEnabled;
                return this;
            }

            public LogLevelStatusBuilder criticalPackages(String criticalPackages) {
                this.criticalPackages = criticalPackages;
                return this;
            }

            public LogLevelStatus build() {
                LogLevelStatus status = new LogLevelStatus();
                status.degradationEnabled = this.degradationEnabled;
                status.isDegraded = this.isDegraded;
                status.degradationLevel = this.degradationLevel;
                status.normalLevel = this.normalLevel;
                status.httpLogEnabled = this.httpLogEnabled;
                status.criticalPackages = this.criticalPackages;
                return status;
            }
        }

        // Getters
        public boolean isDegradationEnabled() { return degradationEnabled; }
        public boolean isDegraded() { return isDegraded; }
        public String getDegradationLevel() { return degradationLevel; }
        public String getNormalLevel() { return normalLevel; }
        public boolean isHttpLogEnabled() { return httpLogEnabled; }
        public String getCriticalPackages() { return criticalPackages; }
    }
}
