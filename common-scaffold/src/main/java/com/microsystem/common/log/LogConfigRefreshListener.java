package com.microsystem.common.log;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.environment.EnvironmentChangeEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * 日志配置刷新监听器
 * 监听配置变更事件，自动刷新日志配置
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LogConfigRefreshListener implements ApplicationListener<EnvironmentChangeEvent> {

    private final LogLevelManager logLevelManager;

    /**
     * 监听的配置键
     */
    private static final String[] LOG_CONFIG_KEYS = {
        "logging.degradation.enabled",
        "logging.degradation.level",
        "logging.normal.level",
        "logging.business.critical.packages",
        "logging.http.enabled"
    };

    @Override
    public void onApplicationEvent(EnvironmentChangeEvent event) {
        Set<String> changedKeys = event.getKeys();
        
        // 检查是否有日志相关配置变更
        boolean logConfigChanged = false;
        for (String key : changedKeys) {
            for (String logConfigKey : LOG_CONFIG_KEYS) {
                if (key.startsWith(logConfigKey)) {
                    logConfigChanged = true;
                    log.info("检测到日志配置变更: {}", key);
                    break;
                }
            }
            if (logConfigChanged) {
                break;
            }
        }
        
        if (logConfigChanged) {
            try {
                // 重新初始化日志级别管理器
                logLevelManager.init();
                log.info("日志配置已刷新");
                
                // 记录配置变更日志
                LogUtils.logBusinessCritical("日志配置刷新", 
                        String.format("变更的配置项: %s", changedKeys));
                
            } catch (Exception e) {
                log.error("刷新日志配置失败", e);
                LogUtils.logException(log, "刷新日志配置", e);
            }
        }
    }
}
