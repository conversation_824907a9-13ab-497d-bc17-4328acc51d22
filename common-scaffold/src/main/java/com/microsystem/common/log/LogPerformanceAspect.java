package com.microsystem.common.log;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

/**
 * 日志性能监控切面
 * 自动记录方法执行时间和性能日志
 * 
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
@ConditionalOnProperty(name = "logging.performance.enabled", havingValue = "true", matchIfMissing = true)
public class LogPerformanceAspect {

    /**
     * 服务层方法切点
     */
    @Pointcut("execution(* com.microsystem.*.service..*(..))")
    public void serviceMethod() {}

    /**
     * 控制器方法切点
     */
    @Pointcut("execution(* com.microsystem.*.controller..*(..))")
    public void controllerMethod() {}

    /**
     * 应用服务方法切点
     */
    @Pointcut("execution(* com.microsystem.*.application.service..*(..))")
    public void applicationServiceMethod() {}

    /**
     * 监控服务层方法性能
     */
    @Around("serviceMethod()")
    public Object monitorServiceMethod(ProceedingJoinPoint joinPoint) throws Throwable {
        return monitorMethodExecution(joinPoint, "SERVICE");
    }

    /**
     * 监控控制器方法性能
     */
    @Around("controllerMethod()")
    public Object monitorControllerMethod(ProceedingJoinPoint joinPoint) throws Throwable {
        return monitorMethodExecution(joinPoint, "CONTROLLER");
    }

    /**
     * 监控应用服务方法性能
     */
    @Around("applicationServiceMethod()")
    public Object monitorApplicationServiceMethod(ProceedingJoinPoint joinPoint) throws Throwable {
        return monitorMethodExecution(joinPoint, "APPLICATION_SERVICE");
    }

    /**
     * 监控方法执行
     */
    private Object monitorMethodExecution(ProceedingJoinPoint joinPoint, String layer) throws Throwable {
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        String fullMethodName = className + "." + methodName;
        
        // 设置方法名到MDC
        LogUtils.setMethodName(fullMethodName);
        
        long startTime = System.currentTimeMillis();
        Object result = null;
        Throwable exception = null;
        
        try {
            result = joinPoint.proceed();
            return result;
            
        } catch (Throwable e) {
            exception = e;
            throw e;
            
        } finally {
            long duration = System.currentTimeMillis() - startTime;
            
            // 记录性能日志
            recordPerformanceLog(layer, fullMethodName, duration, exception, joinPoint.getArgs());
        }
    }

    /**
     * 记录性能日志
     */
    private void recordPerformanceLog(String layer, String methodName, long duration, 
                                    Throwable exception, Object[] args) {
        try {
            // 构建详细信息
            StringBuilder details = new StringBuilder();
            details.append("Layer: ").append(layer);
            details.append(", Args: ").append(args != null ? args.length : 0);
            
            if (exception != null) {
                details.append(", Exception: ").append(exception.getClass().getSimpleName());
                details.append(", Message: ").append(exception.getMessage());
            }
            
            // 根据执行时间决定日志级别
            if (exception != null) {
                // 有异常时记录错误日志
                LogUtils.logPerformance(methodName, duration, details.toString());
                log.error("方法执行异常 - Method: {}, Duration: {}ms, Details: {}", 
                        methodName, duration, details.toString());
                
            } else if (duration > 5000) {
                // 超过5秒记录警告日志
                LogUtils.logPerformance(methodName, duration, details.toString());
                log.warn("方法执行耗时过长 - Method: {}, Duration: {}ms, Details: {}", 
                        methodName, duration, details.toString());
                
            } else if (duration > 1000) {
                // 超过1秒记录性能日志
                LogUtils.logPerformance(methodName, duration, details.toString());
                
            } else {
                // 正常执行时间，记录DEBUG级别
                log.debug("方法执行 - Method: {}, Duration: {}ms", methodName, duration);
            }
            
            // 记录到性能监控日志
            if (duration > 100) { // 只记录超过100ms的方法
                LogUtils.logPerformance(methodName, duration, details.toString());
            }
            
        } catch (Exception e) {
            // 避免日志记录本身出现异常影响业务
            log.debug("记录性能日志失败: {}", e.getMessage());
        }
    }
}
