package com.microsystem.common.log;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.OperatingSystemMXBean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 日志监控组件
 * 监控系统资源使用情况，自动触发日志降级
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "logging.monitor.enabled", havingValue = "true", matchIfMissing = true)
public class LogMonitor {

    private final LogLevelManager logLevelManager;

    // 监控配置
    @Value("${logging.degradation.triggers.cpu-threshold:80}")
    private double cpuThreshold;

    @Value("${logging.degradation.triggers.memory-threshold:85}")
    private double memoryThreshold;

    @Value("${logging.degradation.triggers.qps-threshold:1000}")
    private long qpsThreshold;

    @Value("${logging.degradation.triggers.error-rate-threshold:5}")
    private double errorRateThreshold;

    @Value("${logging.monitor.auto-degradation:true}")
    private boolean autoDegradation;

    // 系统监控Bean
    private final OperatingSystemMXBean osBean;
    private final MemoryMXBean memoryBean;

    // 统计计数器
    private final AtomicLong requestCount = new AtomicLong(0);
    private final AtomicLong errorCount = new AtomicLong(0);
    private final AtomicLong lastRequestCount = new AtomicLong(0);
    private final AtomicLong lastErrorCount = new AtomicLong(0);

    // 监控状态
    private volatile boolean isMonitoring = false;
    private volatile boolean isDegradedByMonitor = false;

    public LogMonitor(LogLevelManager logLevelManager) {
        this.logLevelManager = logLevelManager;
        this.osBean = ManagementFactory.getOperatingSystemMXBean();
        this.memoryBean = ManagementFactory.getMemoryMXBean();
    }

    @PostConstruct
    public void init() {
        isMonitoring = true;
        log.info("日志监控组件已启动 - CPU阈值: {}%, 内存阈值: {}%, QPS阈值: {}, 错误率阈值: {}%", 
                cpuThreshold, memoryThreshold, qpsThreshold, errorRateThreshold);
    }

    /**
     * 定时监控系统资源
     */
    @Scheduled(fixedRate = 30000) // 每30秒检查一次
    public void monitorSystemResources() {
        if (!isMonitoring) {
            return;
        }

        try {
            // 获取系统指标
            double cpuUsage = getCpuUsage();
            double memoryUsage = getMemoryUsage();
            long currentQps = getCurrentQps();
            double errorRate = getCurrentErrorRate();

            // 记录监控指标
            log.debug("系统监控指标 - CPU: {:.2f}%, 内存: {:.2f}%, QPS: {}, 错误率: {:.2f}%", 
                    cpuUsage, memoryUsage, currentQps, errorRate);

            // 检查是否需要降级
            boolean shouldDegrade = shouldTriggerDegradation(cpuUsage, memoryUsage, currentQps, errorRate);

            if (shouldDegrade && !isDegradedByMonitor && autoDegradation) {
                triggerAutoDegradation(cpuUsage, memoryUsage, currentQps, errorRate);
            } else if (!shouldDegrade && isDegradedByMonitor) {
                recoverFromAutoDegradation();
            }

        } catch (Exception e) {
            log.error("系统资源监控异常", e);
        }
    }

    /**
     * 获取CPU使用率
     */
    private double getCpuUsage() {
        try {
            if (osBean instanceof com.sun.management.OperatingSystemMXBean) {
                com.sun.management.OperatingSystemMXBean sunOsBean = 
                        (com.sun.management.OperatingSystemMXBean) osBean;
                return sunOsBean.getProcessCpuLoad() * 100;
            }
        } catch (Exception e) {
            log.debug("获取CPU使用率失败", e);
        }
        return 0.0;
    }

    /**
     * 获取内存使用率
     */
    private double getMemoryUsage() {
        try {
            long used = memoryBean.getHeapMemoryUsage().getUsed();
            long max = memoryBean.getHeapMemoryUsage().getMax();
            return max > 0 ? (double) used / max * 100 : 0.0;
        } catch (Exception e) {
            log.debug("获取内存使用率失败", e);
            return 0.0;
        }
    }

    /**
     * 获取当前QPS
     */
    private long getCurrentQps() {
        long current = requestCount.get();
        long last = lastRequestCount.getAndSet(current);
        return (current - last) / 30; // 30秒间隔
    }

    /**
     * 获取当前错误率
     */
    private double getCurrentErrorRate() {
        long currentRequests = requestCount.get();
        long currentErrors = errorCount.get();
        long lastRequests = lastRequestCount.get();
        long lastErrors = lastErrorCount.getAndSet(currentErrors);

        long requestDiff = currentRequests - lastRequests;
        long errorDiff = currentErrors - lastErrors;

        return requestDiff > 0 ? (double) errorDiff / requestDiff * 100 : 0.0;
    }

    /**
     * 判断是否应该触发降级
     */
    private boolean shouldTriggerDegradation(double cpuUsage, double memoryUsage, 
                                           long currentQps, double errorRate) {
        return cpuUsage > cpuThreshold || 
               memoryUsage > memoryThreshold || 
               currentQps > qpsThreshold || 
               errorRate > errorRateThreshold;
    }

    /**
     * 触发自动降级
     */
    private void triggerAutoDegradation(double cpuUsage, double memoryUsage, 
                                      long currentQps, double errorRate) {
        try {
            logLevelManager.enableDegradation();
            isDegradedByMonitor = true;

            String reason = String.format("系统资源超限 - CPU: %.2f%%, 内存: %.2f%%, QPS: %d, 错误率: %.2f%%", 
                    cpuUsage, memoryUsage, currentQps, errorRate);

            LogUtils.logBusinessCritical("自动日志降级", reason);
            log.warn("触发自动日志降级: {}", reason);

        } catch (Exception e) {
            log.error("触发自动日志降级失败", e);
        }
    }

    /**
     * 从自动降级中恢复
     */
    private void recoverFromAutoDegradation() {
        try {
            logLevelManager.disableDegradation();
            isDegradedByMonitor = false;

            LogUtils.logBusinessCritical("自动日志降级恢复", "系统资源使用率已恢复正常");
            log.info("系统资源使用率已恢复正常，取消自动日志降级");

        } catch (Exception e) {
            log.error("取消自动日志降级失败", e);
        }
    }

    /**
     * 增加请求计数
     */
    public void incrementRequestCount() {
        requestCount.incrementAndGet();
    }

    /**
     * 增加错误计数
     */
    public void incrementErrorCount() {
        errorCount.incrementAndGet();
    }

    /**
     * 获取监控状态
     */
    public MonitorStatus getMonitorStatus() {
        return MonitorStatus.builder()
                .isMonitoring(isMonitoring)
                .isDegradedByMonitor(isDegradedByMonitor)
                .cpuUsage(getCpuUsage())
                .memoryUsage(getMemoryUsage())
                .currentQps(getCurrentQps())
                .errorRate(getCurrentErrorRate())
                .totalRequests(requestCount.get())
                .totalErrors(errorCount.get())
                .build();
    }

    /**
     * 监控状态信息
     */
    public static class MonitorStatus {
        private boolean isMonitoring;
        private boolean isDegradedByMonitor;
        private double cpuUsage;
        private double memoryUsage;
        private long currentQps;
        private double errorRate;
        private long totalRequests;
        private long totalErrors;

        public static MonitorStatusBuilder builder() {
            return new MonitorStatusBuilder();
        }

        public static class MonitorStatusBuilder {
            private boolean isMonitoring;
            private boolean isDegradedByMonitor;
            private double cpuUsage;
            private double memoryUsage;
            private long currentQps;
            private double errorRate;
            private long totalRequests;
            private long totalErrors;

            public MonitorStatusBuilder isMonitoring(boolean isMonitoring) {
                this.isMonitoring = isMonitoring;
                return this;
            }

            public MonitorStatusBuilder isDegradedByMonitor(boolean isDegradedByMonitor) {
                this.isDegradedByMonitor = isDegradedByMonitor;
                return this;
            }

            public MonitorStatusBuilder cpuUsage(double cpuUsage) {
                this.cpuUsage = cpuUsage;
                return this;
            }

            public MonitorStatusBuilder memoryUsage(double memoryUsage) {
                this.memoryUsage = memoryUsage;
                return this;
            }

            public MonitorStatusBuilder currentQps(long currentQps) {
                this.currentQps = currentQps;
                return this;
            }

            public MonitorStatusBuilder errorRate(double errorRate) {
                this.errorRate = errorRate;
                return this;
            }

            public MonitorStatusBuilder totalRequests(long totalRequests) {
                this.totalRequests = totalRequests;
                return this;
            }

            public MonitorStatusBuilder totalErrors(long totalErrors) {
                this.totalErrors = totalErrors;
                return this;
            }

            public MonitorStatus build() {
                MonitorStatus status = new MonitorStatus();
                status.isMonitoring = this.isMonitoring;
                status.isDegradedByMonitor = this.isDegradedByMonitor;
                status.cpuUsage = this.cpuUsage;
                status.memoryUsage = this.memoryUsage;
                status.currentQps = this.currentQps;
                status.errorRate = this.errorRate;
                status.totalRequests = this.totalRequests;
                status.totalErrors = this.totalErrors;
                return status;
            }
        }

        // Getters
        public boolean isMonitoring() { return isMonitoring; }
        public boolean isDegradedByMonitor() { return isDegradedByMonitor; }
        public double getCpuUsage() { return cpuUsage; }
        public double getMemoryUsage() { return memoryUsage; }
        public long getCurrentQps() { return currentQps; }
        public double getErrorRate() { return errorRate; }
        public long getTotalRequests() { return totalRequests; }
        public long getTotalErrors() { return totalErrors; }
    }
}
