package com.microsystem.common.result;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应码枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ResultCode {

    // 成功
    SUCCESS(200, "操作成功"),

    // 客户端错误 4xx
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    CONFLICT(409, "资源冲突"),
    UNPROCESSABLE_ENTITY(422, "请求参数验证失败"),
    TOO_MANY_REQUESTS(429, "请求过于频繁"),

    // 服务端错误 5xx
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    BAD_GATEWAY(502, "网关错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),
    GATEWAY_TIMEOUT(504, "网关超时"),

    // 业务错误 6xxx
    BUSINESS_ERROR(6000, "业务处理失败"),
    DATA_NOT_FOUND(6001, "数据不存在"),
    DATA_ALREADY_EXISTS(6002, "数据已存在"),
    DATA_INVALID(6003, "数据无效"),
    OPERATION_NOT_ALLOWED(6004, "操作不被允许"),

    // 认证授权错误 7xxx
    TOKEN_INVALID(7001, "Token无效"),
    TOKEN_EXPIRED(7002, "Token已过期"),
    TOKEN_MISSING(7003, "Token缺失"),
    PERMISSION_DENIED(7004, "权限不足"),
    ACCOUNT_DISABLED(7005, "账户已禁用"),
    ACCOUNT_LOCKED(7006, "账户已锁定"),
    PASSWORD_ERROR(7007, "密码错误"),
    CAPTCHA_ERROR(7008, "验证码错误"),

    // 第三方服务错误 8xxx
    THIRD_PARTY_SERVICE_ERROR(8000, "第三方服务错误"),
    PAYMENT_ERROR(8001, "支付失败"),
    SMS_SEND_ERROR(8002, "短信发送失败"),
    EMAIL_SEND_ERROR(8003, "邮件发送失败"),

    // 系统错误 9xxx
    SYSTEM_BUSY(9000, "系统繁忙，请稍后重试"),
    SYSTEM_MAINTENANCE(9001, "系统维护中"),
    DATABASE_ERROR(9002, "数据库错误"),
    CACHE_ERROR(9003, "缓存错误"),
    MESSAGE_QUEUE_ERROR(9004, "消息队列错误"),
    FILE_UPLOAD_ERROR(9005, "文件上传失败"),
    FILE_DOWNLOAD_ERROR(9006, "文件下载失败");

    /**
     * 响应码
     */
    private final Integer code;

    /**
     * 响应消息
     */
    private final String message;

    /**
     * 根据code获取枚举
     */
    public static ResultCode getByCode(Integer code) {
        for (ResultCode resultCode : values()) {
            if (resultCode.getCode().equals(code)) {
                return resultCode;
            }
        }
        return INTERNAL_SERVER_ERROR;
    }
}
