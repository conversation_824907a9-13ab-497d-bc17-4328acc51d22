package com.microsystem.common.util;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;

import java.util.UUID;

/**
 * ID生成工具类
 * 
 * <AUTHOR>
 */
public class IdUtils {

    /**
     * 雪花算法实例
     */
    private static final Snowflake SNOWFLAKE = IdUtil.getSnowflake(1, 1);

    /**
     * 生成雪花ID
     */
    public static Long snowflakeId() {
        return SNOWFLAKE.nextId();
    }

    /**
     * 生成雪花ID字符串
     */
    public static String snowflakeIdStr() {
        return String.valueOf(SNOWFLAKE.nextId());
    }

    /**
     * 生成UUID(带连字符)
     */
    public static String uuid() {
        return UUID.randomUUID().toString();
    }

    /**
     * 生成UUID(不带连字符)
     */
    public static String simpleUuid() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成ObjectId
     */
    public static String objectId() {
        return IdUtil.objectId();
    }

    /**
     * 生成随机字符串ID
     */
    public static String randomId(int length) {
        return IdUtil.randomUUID().substring(0, length);
    }

    /**
     * 生成纳米ID
     */
    public static String nanoId() {
        return IdUtil.nanoId();
    }

    /**
     * 生成指定长度的纳米ID
     */
    public static String nanoId(int size) {
        return IdUtil.nanoId(size);
    }
}
