package com.microsystem.common.constant;

/**
 * 通用常量定义
 * 
 * <AUTHOR>
 */
public interface CommonConstants {

    /**
     * 系统编码
     */
    String UTF8 = "UTF-8";

    /**
     * 成功标识
     */
    String SUCCESS = "success";

    /**
     * 失败标识
     */
    String FAIL = "fail";

    /**
     * 是否删除标识
     */
    interface DeleteFlag {
        /** 未删除 */
        Integer NOT_DELETED = 0;
        /** 已删除 */
        Integer DELETED = 1;
    }

    /**
     * 状态标识
     */
    interface Status {
        /** 启用 */
        Integer ENABLED = 1;
        /** 禁用 */
        Integer DISABLED = 0;
    }

    /**
     * 性别标识
     */
    interface Gender {
        /** 男 */
        Integer MALE = 1;
        /** 女 */
        Integer FEMALE = 2;
        /** 未知 */
        Integer UNKNOWN = 0;
    }

    /**
     * 缓存相关常量
     */
    interface Cache {
        /** 默认过期时间(秒) */
        long DEFAULT_EXPIRE = 3600;
        /** 短期过期时间(秒) */
        long SHORT_EXPIRE = 300;
        /** 长期过期时间(秒) */
        long LONG_EXPIRE = 86400;
        /** 永不过期 */
        long NEVER_EXPIRE = -1;
    }

    /**
     * 分页相关常量
     */
    interface Page {
        /** 默认页码 */
        long DEFAULT_CURRENT = 1;
        /** 默认每页大小 */
        long DEFAULT_SIZE = 10;
        /** 最大每页大小 */
        long MAX_SIZE = 1000;
    }

    /**
     * 日期时间格式常量
     */
    interface DateFormat {
        String YYYY_MM_DD = "yyyy-MM-dd";
        String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
        String YYYY_MM_DD_HH_MM_SS_SSS = "yyyy-MM-dd HH:mm:ss.SSS";
        String YYYYMMDD = "yyyyMMdd";
        String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";
    }

    /**
     * HTTP请求头常量
     */
    interface Header {
        String AUTHORIZATION = "Authorization";
        String CONTENT_TYPE = "Content-Type";
        String USER_AGENT = "User-Agent";
        String X_FORWARDED_FOR = "X-Forwarded-For";
        String X_REAL_IP = "X-Real-IP";
        String TRACE_ID = "X-Trace-Id";
    }

    /**
     * 正则表达式常量
     */
    interface Regex {
        /** 手机号 */
        String MOBILE = "^1[3-9]\\d{9}$";
        /** 邮箱 */
        String EMAIL = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
        /** 身份证号 */
        String ID_CARD = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";
        /** 密码(8-20位，包含字母和数字) */
        String PASSWORD = "^(?=.*[a-zA-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,20}$";
    }

    /**
     * 文件相关常量
     */
    interface File {
        /** 最大文件大小(10MB) */
        long MAX_SIZE = 10 * 1024 * 1024;
        /** 允许的图片格式 */
        String[] IMAGE_EXTENSIONS = {"jpg", "jpeg", "png", "gif", "bmp", "webp"};
        /** 允许的文档格式 */
        String[] DOCUMENT_EXTENSIONS = {"pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt"};
    }
}
