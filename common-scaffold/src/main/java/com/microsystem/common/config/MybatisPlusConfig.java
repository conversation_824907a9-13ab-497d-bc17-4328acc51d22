package com.microsystem.common.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.BlockAttackInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * MyBatis Plus配置类
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class MybatisPlusConfig {

    /**
     * MyBatis Plus拦截器配置
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        
        // 分页插件
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        
        // 乐观锁插件
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
        
        // 防止全表更新与删除插件
        interceptor.addInnerInterceptor(new BlockAttackInnerInterceptor());
        
        return interceptor;
    }

    /**
     * 字段自动填充处理器
     */
    @Component
    public static class CustomMetaObjectHandler implements MetaObjectHandler {

        @Override
        public void insertFill(MetaObject metaObject) {
            log.debug("开始插入填充...");
            
            // 填充创建时间
            this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, LocalDateTime.now());
            
            // 填充更新时间
            this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
            
            // 填充创建人ID (这里可以从当前登录用户获取)
            // Long currentUserId = getCurrentUserId();
            // this.strictInsertFill(metaObject, "createBy", Long.class, currentUserId);
            
            // 填充更新人ID
            // this.strictInsertFill(metaObject, "updateBy", Long.class, currentUserId);
        }

        @Override
        public void updateFill(MetaObject metaObject) {
            log.debug("开始更新填充...");
            
            // 填充更新时间
            this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
            
            // 填充更新人ID (这里可以从当前登录用户获取)
            // Long currentUserId = getCurrentUserId();
            // this.strictUpdateFill(metaObject, "updateBy", Long.class, currentUserId);
        }

        /**
         * 获取当前登录用户ID
         * 这里需要根据实际的认证方式来实现
         */
        private Long getCurrentUserId() {
            // TODO: 从SecurityContext或其他地方获取当前用户ID
            return 1L; // 临时返回固定值
        }
    }
}
