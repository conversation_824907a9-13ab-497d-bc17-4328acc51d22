package com.microsystem.auth.service;

import com.microsystem.auth.domain.entity.User;
import com.microsystem.auth.dto.request.LoginRequest;
import com.microsystem.auth.dto.request.RegisterRequest;
import com.microsystem.auth.dto.response.LoginResponse;
import com.microsystem.auth.mapper.UserMapper;
import com.microsystem.auth.util.JwtUtils;
import com.microsystem.common.exception.BusinessException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.crypto.password.PasswordEncoder;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 用户服务测试类
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class UserServiceTest {

    @Mock
    private UserMapper userMapper;

    @Mock
    private PasswordEncoder passwordEncoder;

    @Mock
    private JwtUtils jwtUtils;

    @InjectMocks
    private UserServiceImpl userService;

    private User testUser;
    private LoginRequest loginRequest;
    private RegisterRequest registerRequest;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testUser = new User();
        testUser.setId(1L);
        testUser.setUsername("testuser");
        testUser.setPassword("$2a$10$encodedPassword");
        testUser.setNickname("Test User");
        testUser.setEmail("<EMAIL>");
        testUser.setMobile("13800138000");
        testUser.setStatus(1);

        loginRequest = new LoginRequest();
        loginRequest.setUsername("testuser");
        loginRequest.setPassword("password123");

        registerRequest = new RegisterRequest();
        registerRequest.setUsername("newuser");
        registerRequest.setPassword("password123");
        registerRequest.setConfirmPassword("password123");
        registerRequest.setNickname("New User");
        registerRequest.setEmail("<EMAIL>");
        registerRequest.setMobile("***********");
    }

    @Test
    void testLoginSuccess() {
        // Given
        when(userMapper.findByUsername("testuser")).thenReturn(testUser);
        when(passwordEncoder.matches("password123", "$2a$10$encodedPassword")).thenReturn(true);
        when(jwtUtils.generateAccessToken(1L, "testuser")).thenReturn("access_token");
        when(jwtUtils.generateRefreshToken(1L, "testuser")).thenReturn("refresh_token");

        // When
        LoginResponse response = userService.login(loginRequest);

        // Then
        assertNotNull(response);
        assertEquals("access_token", response.getAccessToken());
        assertEquals("refresh_token", response.getRefreshToken());
        assertEquals("Bearer", response.getTokenType());
        assertNotNull(response.getUserInfo());
        assertEquals("testuser", response.getUserInfo().getUsername());

        verify(userMapper).findByUsername("testuser");
        verify(passwordEncoder).matches("password123", "$2a$10$encodedPassword");
        verify(jwtUtils).generateAccessToken(1L, "testuser");
        verify(jwtUtils).generateRefreshToken(1L, "testuser");
    }

    @Test
    void testLoginWithInvalidUsername() {
        // Given
        when(userMapper.findByUsername("invaliduser")).thenReturn(null);

        loginRequest.setUsername("invaliduser");

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            userService.login(loginRequest);
        });

        assertEquals("用户名或密码错误", exception.getMessage());
        verify(userMapper).findByUsername("invaliduser");
        verify(passwordEncoder, never()).matches(anyString(), anyString());
    }

    @Test
    void testLoginWithInvalidPassword() {
        // Given
        when(userMapper.findByUsername("testuser")).thenReturn(testUser);
        when(passwordEncoder.matches("wrongpassword", "$2a$10$encodedPassword")).thenReturn(false);

        loginRequest.setPassword("wrongpassword");

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            userService.login(loginRequest);
        });

        assertEquals("用户名或密码错误", exception.getMessage());
        verify(userMapper).findByUsername("testuser");
        verify(passwordEncoder).matches("wrongpassword", "$2a$10$encodedPassword");
    }

    @Test
    void testRegisterSuccess() {
        // Given
        when(userMapper.findByUsername("newuser")).thenReturn(null);
        when(userMapper.findByEmail("<EMAIL>")).thenReturn(null);
        when(userMapper.findByMobile("***********")).thenReturn(null);
        when(passwordEncoder.encode("password123")).thenReturn("$2a$10$encodedNewPassword");
        when(userMapper.insert(any(User.class))).thenReturn(1);

        // When
        assertDoesNotThrow(() -> {
            userService.register(registerRequest);
        });

        // Then
        verify(userMapper).findByUsername("newuser");
        verify(userMapper).findByEmail("<EMAIL>");
        verify(userMapper).findByMobile("***********");
        verify(passwordEncoder).encode("password123");
        verify(userMapper).insert(any(User.class));
    }

    @Test
    void testRegisterWithExistingUsername() {
        // Given
        when(userMapper.findByUsername("newuser")).thenReturn(testUser);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            userService.register(registerRequest);
        });

        assertEquals("用户名已存在", exception.getMessage());
        verify(userMapper).findByUsername("newuser");
        verify(userMapper, never()).insert(any(User.class));
    }

    @Test
    void testRegisterWithPasswordMismatch() {
        // Given
        registerRequest.setConfirmPassword("differentpassword");

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            userService.register(registerRequest);
        });

        assertEquals("两次输入的密码不一致", exception.getMessage());
        verify(userMapper, never()).findByUsername(anyString());
        verify(userMapper, never()).insert(any(User.class));
    }

    @Test
    void testFindByUsername() {
        // Given
        when(userMapper.findByUsername("testuser")).thenReturn(testUser);

        // When
        User result = userService.findByUsername("testuser");

        // Then
        assertNotNull(result);
        assertEquals("testuser", result.getUsername());
        assertEquals("<EMAIL>", result.getEmail());
        verify(userMapper).findByUsername("testuser");
    }

    @Test
    void testFindByUsernameNotFound() {
        // Given
        when(userMapper.findByUsername("nonexistent")).thenReturn(null);

        // When
        User result = userService.findByUsername("nonexistent");

        // Then
        assertNull(result);
        verify(userMapper).findByUsername("nonexistent");
    }
}
