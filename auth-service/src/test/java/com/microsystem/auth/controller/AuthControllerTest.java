package com.microsystem.auth.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.microsystem.auth.dto.request.LoginRequest;
import com.microsystem.auth.dto.request.RegisterRequest;
import com.microsystem.auth.dto.response.LoginResponse;
import com.microsystem.auth.dto.response.UserInfoResponse;
import com.microsystem.auth.service.UserService;
import com.microsystem.common.exception.BusinessException;
import com.microsystem.common.result.ResultCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 认证控制器测试类
 * 
 * <AUTHOR>
 */
@WebMvcTest(AuthController.class)
class AuthControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private UserService userService;

    @Autowired
    private ObjectMapper objectMapper;

    private LoginRequest loginRequest;
    private RegisterRequest registerRequest;
    private LoginResponse loginResponse;
    private UserInfoResponse userInfoResponse;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        loginRequest = new LoginRequest();
        loginRequest.setUsername("testuser");
        loginRequest.setPassword("password123");

        registerRequest = new RegisterRequest();
        registerRequest.setUsername("newuser");
        registerRequest.setPassword("password123");
        registerRequest.setConfirmPassword("password123");
        registerRequest.setNickname("New User");
        registerRequest.setEmail("<EMAIL>");
        registerRequest.setMobile("13800138001");

        userInfoResponse = new UserInfoResponse();
        userInfoResponse.setId(1L);
        userInfoResponse.setUsername("testuser");
        userInfoResponse.setNickname("Test User");
        userInfoResponse.setEmail("<EMAIL>");

        loginResponse = new LoginResponse();
        loginResponse.setAccessToken("access_token");
        loginResponse.setRefreshToken("refresh_token");
        loginResponse.setTokenType("Bearer");
        loginResponse.setExpiresIn(86400L);
        loginResponse.setUserInfo(userInfoResponse);
    }

    @Test
    void testLoginSuccess() throws Exception {
        // Given
        when(userService.login(any(LoginRequest.class))).thenReturn(loginResponse);

        // When & Then
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("操作成功"))
                .andExpect(jsonPath("$.data.accessToken").value("access_token"))
                .andExpect(jsonPath("$.data.refreshToken").value("refresh_token"))
                .andExpect(jsonPath("$.data.tokenType").value("Bearer"))
                .andExpect(jsonPath("$.data.userInfo.username").value("testuser"));

        verify(userService).login(any(LoginRequest.class));
    }

    @Test
    void testLoginWithInvalidCredentials() throws Exception {
        // Given
        when(userService.login(any(LoginRequest.class)))
                .thenThrow(new BusinessException(ResultCode.UNAUTHORIZED, "用户名或密码错误"));

        // When & Then
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.code").value(401))
                .andExpect(jsonPath("$.message").value("用户名或密码错误"));

        verify(userService).login(any(LoginRequest.class));
    }

    @Test
    void testLoginWithInvalidInput() throws Exception {
        // Given
        loginRequest.setUsername(""); // 空用户名
        loginRequest.setPassword(""); // 空密码

        // When & Then
        mockMvc.perform(post("/api/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value(400));

        verify(userService, never()).login(any(LoginRequest.class));
    }

    @Test
    void testRegisterSuccess() throws Exception {
        // Given
        doNothing().when(userService).register(any(RegisterRequest.class));

        // When & Then
        mockMvc.perform(post("/api/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(registerRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("注册成功"));

        verify(userService).register(any(RegisterRequest.class));
    }

    @Test
    void testRegisterWithExistingUsername() throws Exception {
        // Given
        doThrow(new BusinessException(ResultCode.DATA_ALREADY_EXISTS, "用户名已存在"))
                .when(userService).register(any(RegisterRequest.class));

        // When & Then
        mockMvc.perform(post("/api/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(registerRequest)))
                .andExpect(status().isConflict())
                .andExpect(jsonPath("$.code").value(409))
                .andExpect(jsonPath("$.message").value("用户名已存在"));

        verify(userService).register(any(RegisterRequest.class));
    }

    @Test
    void testRegisterWithInvalidInput() throws Exception {
        // Given
        registerRequest.setUsername(""); // 空用户名
        registerRequest.setPassword("123"); // 密码太短
        registerRequest.setEmail("invalid-email"); // 无效邮箱

        // When & Then
        mockMvc.perform(post("/api/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(registerRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value(400));

        verify(userService, never()).register(any(RegisterRequest.class));
    }

    @Test
    void testRefreshTokenSuccess() throws Exception {
        // Given
        when(userService.refreshToken("valid_refresh_token")).thenReturn(loginResponse);

        // When & Then
        mockMvc.perform(post("/api/auth/refresh")
                .param("refreshToken", "valid_refresh_token"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.accessToken").value("access_token"));

        verify(userService).refreshToken("valid_refresh_token");
    }

    @Test
    void testRefreshTokenWithInvalidToken() throws Exception {
        // Given
        when(userService.refreshToken("invalid_refresh_token"))
                .thenThrow(new BusinessException(ResultCode.UNAUTHORIZED, "刷新Token无效"));

        // When & Then
        mockMvc.perform(post("/api/auth/refresh")
                .param("refreshToken", "invalid_refresh_token"))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.code").value(401))
                .andExpect(jsonPath("$.message").value("刷新Token无效"));

        verify(userService).refreshToken("invalid_refresh_token");
    }

    @Test
    void testLogoutSuccess() throws Exception {
        // Given
        doNothing().when(userService).logout("valid_token");

        // When & Then
        mockMvc.perform(post("/api/auth/logout")
                .header("Authorization", "Bearer valid_token"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("登出成功"));

        verify(userService).logout("valid_token");
    }
}
