<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds">
    
    <!-- 引入Spring Boot默认配置 -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    
    <!-- 定义日志文件路径 -->
    <springProfile name="!docker">
        <property name="LOG_PATH" value="./logs/auth-service"/>
    </springProfile>
    <springProfile name="docker">
        <property name="LOG_PATH" value="/app/logs"/>
    </springProfile>
    
    <!-- 定义日志格式 -->
    <property name="LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-}] [%X{userId:-}] %logger{50} - %msg%n"/>
    <property name="ASYNC_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-}] [%X{userId:-}] %logger{50} - %msg%n"/>
    
    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
    </appender>
    
    <!-- 异步控制台输出 -->
    <appender name="ASYNC_CONSOLE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="CONSOLE"/>
        <queueSize>1024</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <maxFlushTime>2000</maxFlushTime>
        <neverBlock>true</neverBlock>
    </appender>
    
    <!-- 应用日志文件 -->
    <appender name="APP_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/auth-service.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/auth-service.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    
    <!-- 异步应用日志 -->
    <appender name="ASYNC_APP_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="APP_FILE"/>
        <queueSize>2048</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <maxFlushTime>2000</maxFlushTime>
        <neverBlock>true</neverBlock>
    </appender>
    
    <!-- 错误日志文件 -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/auth-service-error.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/auth-service-error.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>60</maxHistory>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    
    <!-- 异步错误日志 -->
    <appender name="ASYNC_ERROR_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="ERROR_FILE"/>
        <queueSize>1024</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <maxFlushTime>2000</maxFlushTime>
        <neverBlock>true</neverBlock>
    </appender>
    
    <!-- HTTP请求日志 -->
    <appender name="HTTP_REQUEST_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/auth-service-http-request.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/auth-service-http-request.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>200MB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>2GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- 异步HTTP请求日志 -->
    <appender name="ASYNC_HTTP_REQUEST_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="HTTP_REQUEST_FILE"/>
        <queueSize>4096</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <maxFlushTime>1000</maxFlushTime>
        <neverBlock>true</neverBlock>
    </appender>
    
    <!-- HTTP响应日志 -->
    <appender name="HTTP_RESPONSE_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/auth-service-http-response.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/auth-service-http-response.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>200MB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>2GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- 异步HTTP响应日志 -->
    <appender name="ASYNC_HTTP_RESPONSE_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="HTTP_RESPONSE_FILE"/>
        <queueSize>4096</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <maxFlushTime>1000</maxFlushTime>
        <neverBlock>true</neverBlock>
    </appender>
    
    <!-- 业务关键操作日志 -->
    <appender name="BUSINESS_CRITICAL_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/auth-service-business-critical.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/auth-service-business-critical.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>90</maxHistory>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- 异步业务关键操作日志 -->
    <appender name="ASYNC_BUSINESS_CRITICAL_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="BUSINESS_CRITICAL_FILE"/>
        <queueSize>2048</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <maxFlushTime>2000</maxFlushTime>
        <neverBlock>true</neverBlock>
    </appender>
    
    <!-- 安全审计日志 -->
    <appender name="SECURITY_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/auth-service-security.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/auth-service-security.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>365</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- 异步安全审计日志 -->
    <appender name="ASYNC_SECURITY_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="SECURITY_FILE"/>
        <queueSize>2048</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <maxFlushTime>2000</maxFlushTime>
        <neverBlock>true</neverBlock>
    </appender>
    
    <!-- 特定Logger配置 -->
    <logger name="HTTP_REQUEST" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_HTTP_REQUEST_FILE"/>
    </logger>
    
    <logger name="HTTP_RESPONSE" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_HTTP_RESPONSE_FILE"/>
    </logger>
    
    <logger name="BUSINESS_CRITICAL" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_BUSINESS_CRITICAL_FILE"/>
    </logger>
    
    <logger name="SECURITY" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_SECURITY_FILE"/>
    </logger>
    
    <!-- 应用包日志级别 -->
    <logger name="com.microsystem.auth" level="INFO"/>
    <logger name="com.microsystem.common" level="INFO"/>
    
    <!-- 第三方库日志级别 -->
    <logger name="org.springframework" level="WARN"/>
    <logger name="org.apache" level="WARN"/>
    <logger name="com.alibaba" level="WARN"/>
    <logger name="io.lettuce" level="WARN"/>
    <logger name="com.zaxxer.hikari" level="WARN"/>
    
    <!-- 根Logger配置 -->
    <springProfile name="dev,test">
        <root level="DEBUG">
            <appender-ref ref="ASYNC_CONSOLE"/>
            <appender-ref ref="ASYNC_APP_FILE"/>
            <appender-ref ref="ASYNC_ERROR_FILE"/>
        </root>
    </springProfile>
    
    <springProfile name="prod,docker">
        <root level="INFO">
            <appender-ref ref="ASYNC_APP_FILE"/>
            <appender-ref ref="ASYNC_ERROR_FILE"/>
        </root>
    </springProfile>
    
</configuration>
