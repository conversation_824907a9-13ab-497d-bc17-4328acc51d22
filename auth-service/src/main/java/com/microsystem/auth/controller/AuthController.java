package com.microsystem.auth.controller;

import com.microsystem.auth.dto.request.LoginRequest;
import com.microsystem.auth.dto.request.RegisterRequest;
import com.microsystem.auth.dto.response.LoginResponse;
import com.microsystem.auth.dto.response.UserInfoResponse;
import com.microsystem.auth.service.UserService;
import com.microsystem.common.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

/**
 * 认证控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
@Tag(name = "认证管理", description = "用户认证相关接口")
public class AuthController {

    private final UserService userService;

    /**
     * 用户登录
     */
    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "用户名密码登录")
    public Result<LoginResponse> login(@Valid @RequestBody LoginRequest request, HttpServletRequest httpRequest) {
        log.info("用户登录: {}", request.getUsername());
        
        // 获取客户端IP
        String clientIp = getClientIp(httpRequest);
        
        LoginResponse response = userService.login(request);
        
        // 更新最后登录信息
        if (response.getUserInfo() != null) {
            userService.updateLastLoginInfo(response.getUserInfo().getId(), clientIp);
        }
        
        return Result.success(response);
    }

    /**
     * 用户注册
     */
    @PostMapping("/register")
    @Operation(summary = "用户注册", description = "新用户注册")
    public Result<Void> register(@Valid @RequestBody RegisterRequest request) {
        log.info("用户注册: {}", request.getUsername());
        
        userService.register(request);
        
        return Result.success("注册成功");
    }

    /**
     * 刷新Token
     */
    @PostMapping("/refresh")
    @Operation(summary = "刷新Token", description = "使用刷新Token获取新的访问Token")
    public Result<LoginResponse> refreshToken(@RequestParam String refreshToken) {
        log.info("刷新Token");
        
        LoginResponse response = userService.refreshToken(refreshToken);
        
        return Result.success(response);
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/userinfo")
    @Operation(summary = "获取用户信息", description = "获取当前登录用户的详细信息")
    public Result<UserInfoResponse> getUserInfo(Authentication authentication) {
        log.info("获取用户信息: {}", authentication.getName());
        
        // 从认证信息中获取用户ID
        Long userId = getCurrentUserId(authentication);
        UserInfoResponse userInfo = userService.getUserInfo(userId);
        
        return Result.success(userInfo);
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    @Operation(summary = "用户登出", description = "用户退出登录")
    public Result<Void> logout(HttpServletRequest request) {
        log.info("用户登出");
        
        String token = getTokenFromRequest(request);
        if (token != null) {
            userService.logout(token);
        }
        
        return Result.success("登出成功");
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    /**
     * 从请求中获取Token
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }

    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId(Authentication authentication) {
        // 这里需要根据实际的UserDetails实现来获取用户ID
        // 假设UserDetailsImpl中有getId方法
        if (authentication.getPrincipal() instanceof com.microsystem.auth.security.UserDetailsImpl userDetails) {
            return userDetails.getId();
        }
        return null;
    }
}
