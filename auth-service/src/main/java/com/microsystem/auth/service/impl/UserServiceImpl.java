package com.microsystem.auth.service.impl;

import com.microsystem.auth.domain.entity.User;
import com.microsystem.auth.dto.request.LoginRequest;
import com.microsystem.auth.dto.request.RegisterRequest;
import com.microsystem.auth.dto.response.LoginResponse;
import com.microsystem.auth.dto.response.UserInfoResponse;
import com.microsystem.auth.mapper.UserMapper;
import com.microsystem.auth.service.UserService;
import com.microsystem.auth.util.JwtUtils;
import com.microsystem.common.exception.BusinessException;
import com.microsystem.common.log.LogUtils;
import com.microsystem.common.result.ResultCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final UserMapper userMapper;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtils jwtUtils;

    @Override
    public LoginResponse login(LoginRequest request) {
        long startTime = System.currentTimeMillis();
        String clientIp = getClientIp();
        
        try {
            LogUtils.setUserId(request.getUsername());
            
            // 查询用户
            User user = userMapper.findByUsername(request.getUsername());
            if (user == null) {
                LogUtils.logUserLogin(request.getUsername(), clientIp, false);
                LogUtils.logSecurity("登录失败", 
                        String.format("用户不存在: %s, IP: %s", request.getUsername(), clientIp), "WARN");
                throw new BusinessException(ResultCode.UNAUTHORIZED, "用户名或密码错误");
            }

            // 验证密码
            if (!passwordEncoder.matches(request.getPassword(), user.getPassword())) {
                LogUtils.logUserLogin(request.getUsername(), clientIp, false);
                LogUtils.logSecurity("登录失败", 
                        String.format("密码错误: %s, IP: %s", request.getUsername(), clientIp), "WARN");
                throw new BusinessException(ResultCode.UNAUTHORIZED, "用户名或密码错误");
            }

            // 检查用户状态
            if (user.getStatus() != 1) {
                LogUtils.logUserLogin(request.getUsername(), clientIp, false);
                LogUtils.logSecurity("登录失败", 
                        String.format("用户已禁用: %s, IP: %s", request.getUsername(), clientIp), "WARN");
                throw new BusinessException(ResultCode.FORBIDDEN, "用户已被禁用");
            }

            // 生成Token
            String accessToken = jwtUtils.generateAccessToken(user.getId(), user.getUsername());
            String refreshToken = jwtUtils.generateRefreshToken(user.getId(), user.getUsername());

            // 更新最后登录信息
            updateLastLoginInfo(user.getId(), clientIp);

            // 构建响应
            UserInfoResponse userInfo = UserInfoResponse.builder()
                    .id(user.getId())
                    .username(user.getUsername())
                    .nickname(user.getNickname())
                    .email(user.getEmail())
                    .mobile(user.getMobile())
                    .avatar(user.getAvatar())
                    .build();

            LoginResponse response = LoginResponse.builder()
                    .accessToken(accessToken)
                    .refreshToken(refreshToken)
                    .tokenType("Bearer")
                    .expiresIn(86400L)
                    .userInfo(userInfo)
                    .build();

            // 记录成功日志
            LogUtils.logUserLogin(request.getUsername(), clientIp, true);
            LogUtils.logBusinessCritical("用户登录", 
                    String.format("用户: %s, IP: %s, 耗时: %dms", 
                            request.getUsername(), clientIp, System.currentTimeMillis() - startTime));

            return response;

        } catch (Exception e) {
            LogUtils.logException(log, "用户登录", e);
            throw e;
        } finally {
            LogUtils.logMethodExecution(log, "login", System.currentTimeMillis() - startTime);
        }
    }

    @Override
    public void register(RegisterRequest request) {
        long startTime = System.currentTimeMillis();
        String clientIp = getClientIp();
        
        try {
            // 验证密码一致性
            if (!request.getPassword().equals(request.getConfirmPassword())) {
                throw new BusinessException(ResultCode.PARAM_ERROR, "两次输入的密码不一致");
            }

            // 检查用户名是否存在
            if (userMapper.findByUsername(request.getUsername()) != null) {
                LogUtils.logSecurity("注册失败", 
                        String.format("用户名已存在: %s, IP: %s", request.getUsername(), clientIp), "WARN");
                throw new BusinessException(ResultCode.DATA_ALREADY_EXISTS, "用户名已存在");
            }

            // 检查邮箱是否存在
            if (userMapper.findByEmail(request.getEmail()) != null) {
                throw new BusinessException(ResultCode.DATA_ALREADY_EXISTS, "邮箱已被注册");
            }

            // 检查手机号是否存在
            if (userMapper.findByMobile(request.getMobile()) != null) {
                throw new BusinessException(ResultCode.DATA_ALREADY_EXISTS, "手机号已被注册");
            }

            // 创建用户
            User user = new User();
            user.setUsername(request.getUsername());
            user.setPassword(passwordEncoder.encode(request.getPassword()));
            user.setNickname(request.getNickname());
            user.setEmail(request.getEmail());
            user.setMobile(request.getMobile());
            user.setStatus(1);
            user.setCreateTime(LocalDateTime.now());
            user.setUpdateTime(LocalDateTime.now());

            userMapper.insert(user);

            // 记录注册日志
            LogUtils.logUserRegister(request.getUsername(), request.getEmail(), clientIp);
            LogUtils.logBusinessCritical("用户注册", 
                    String.format("用户: %s, 邮箱: %s, IP: %s", 
                            request.getUsername(), request.getEmail(), clientIp));

        } catch (Exception e) {
            LogUtils.logException(log, "用户注册", e);
            throw e;
        } finally {
            LogUtils.logMethodExecution(log, "register", System.currentTimeMillis() - startTime);
        }
    }

    @Override
    public User findByUsername(String username) {
        long startTime = System.currentTimeMillis();
        try {
            User user = userMapper.findByUsername(username);
            LogUtils.logDatabaseOperation("SELECT", "users", "username=" + username, 
                    System.currentTimeMillis() - startTime);
            return user;
        } finally {
            LogUtils.logMethodExecution(log, "findByUsername", System.currentTimeMillis() - startTime);
        }
    }

    @Override
    public User findById(Long userId) {
        long startTime = System.currentTimeMillis();
        try {
            User user = userMapper.findById(userId);
            LogUtils.logDatabaseOperation("SELECT", "users", "id=" + userId, 
                    System.currentTimeMillis() - startTime);
            return user;
        } finally {
            LogUtils.logMethodExecution(log, "findById", System.currentTimeMillis() - startTime);
        }
    }

    @Override
    public List<String> getUserPermissions(Long userId) {
        long startTime = System.currentTimeMillis();
        try {
            List<String> permissions = userMapper.getUserPermissions(userId);
            LogUtils.logDatabaseOperation("SELECT", "user_permissions", "userId=" + userId, 
                    System.currentTimeMillis() - startTime);
            return permissions;
        } finally {
            LogUtils.logMethodExecution(log, "getUserPermissions", System.currentTimeMillis() - startTime);
        }
    }

    @Override
    public List<String> getUserRoles(Long userId) {
        long startTime = System.currentTimeMillis();
        try {
            List<String> roles = userMapper.getUserRoles(userId);
            LogUtils.logDatabaseOperation("SELECT", "user_roles", "userId=" + userId, 
                    System.currentTimeMillis() - startTime);
            return roles;
        } finally {
            LogUtils.logMethodExecution(log, "getUserRoles", System.currentTimeMillis() - startTime);
        }
    }

    @Override
    public UserInfoResponse getUserInfo(Long userId) {
        long startTime = System.currentTimeMillis();
        try {
            User user = findById(userId);
            if (user == null) {
                throw new BusinessException(ResultCode.DATA_NOT_FOUND, "用户不存在");
            }

            return UserInfoResponse.builder()
                    .id(user.getId())
                    .username(user.getUsername())
                    .nickname(user.getNickname())
                    .email(user.getEmail())
                    .mobile(user.getMobile())
                    .avatar(user.getAvatar())
                    .build();
        } finally {
            LogUtils.logMethodExecution(log, "getUserInfo", System.currentTimeMillis() - startTime);
        }
    }

    @Override
    public void updateLastLoginInfo(Long userId, String loginIp) {
        long startTime = System.currentTimeMillis();
        try {
            userMapper.updateLastLoginInfo(userId, loginIp, LocalDateTime.now());
            LogUtils.logDatabaseOperation("UPDATE", "users", "id=" + userId, 
                    System.currentTimeMillis() - startTime);
        } finally {
            LogUtils.logMethodExecution(log, "updateLastLoginInfo", System.currentTimeMillis() - startTime);
        }
    }

    @Override
    public LoginResponse refreshToken(String refreshToken) {
        long startTime = System.currentTimeMillis();
        try {
            // 验证刷新Token
            if (!jwtUtils.validateToken(refreshToken)) {
                throw new BusinessException(ResultCode.UNAUTHORIZED, "刷新Token无效");
            }

            // 获取用户信息
            Long userId = jwtUtils.getUserIdFromToken(refreshToken);
            String username = jwtUtils.getUsernameFromToken(refreshToken);

            // 生成新的Token
            String newAccessToken = jwtUtils.generateAccessToken(userId, username);
            String newRefreshToken = jwtUtils.generateRefreshToken(userId, username);

            LoginResponse response = LoginResponse.builder()
                    .accessToken(newAccessToken)
                    .refreshToken(newRefreshToken)
                    .tokenType("Bearer")
                    .expiresIn(86400L)
                    .build();

            LogUtils.logBusinessCritical("Token刷新", 
                    String.format("用户ID: %s, 用户名: %s", userId, username));

            return response;

        } catch (Exception e) {
            LogUtils.logException(log, "刷新Token", e);
            throw e;
        } finally {
            LogUtils.logMethodExecution(log, "refreshToken", System.currentTimeMillis() - startTime);
        }
    }

    @Override
    public void logout(String token) {
        long startTime = System.currentTimeMillis();
        try {
            // 这里可以将Token加入黑名单
            // tokenBlacklistService.addToBlacklist(token);
            
            String username = jwtUtils.getUsernameFromToken(token);
            LogUtils.logBusinessCritical("用户登出", String.format("用户: %s", username));
            
        } catch (Exception e) {
            LogUtils.logException(log, "用户登出", e);
            throw e;
        } finally {
            LogUtils.logMethodExecution(log, "logout", System.currentTimeMillis() - startTime);
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String ip = request.getHeader("X-Forwarded-For");
                if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
                    ip = request.getHeader("X-Real-IP");
                }
                if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
                    ip = request.getRemoteAddr();
                }
                return ip;
            }
        } catch (Exception e) {
            log.debug("获取客户端IP失败", e);
        }
        return "unknown";
    }
}
