package com.microsystem.auth.service;

import com.microsystem.auth.domain.entity.User;
import com.microsystem.auth.dto.request.LoginRequest;
import com.microsystem.auth.dto.request.RegisterRequest;
import com.microsystem.auth.dto.response.LoginResponse;
import com.microsystem.auth.dto.response.UserInfoResponse;

import java.util.List;

/**
 * 用户服务接口
 * 
 * <AUTHOR>
 */
public interface UserService {

    /**
     * 用户登录
     */
    LoginResponse login(LoginRequest request);

    /**
     * 用户注册
     */
    void register(RegisterRequest request);

    /**
     * 根据用户名查询用户
     */
    User findByUsername(String username);

    /**
     * 根据用户ID查询用户
     */
    User findById(Long userId);

    /**
     * 获取用户权限列表
     */
    List<String> getUserPermissions(Long userId);

    /**
     * 获取用户角色列表
     */
    List<String> getUserRoles(Long userId);

    /**
     * 获取用户信息
     */
    UserInfoResponse getUserInfo(Long userId);

    /**
     * 更新用户最后登录信息
     */
    void updateLastLoginInfo(Long userId, String loginIp);

    /**
     * 刷新Token
     */
    LoginResponse refreshToken(String refreshToken);

    /**
     * 用户登出
     */
    void logout(String token);
}
