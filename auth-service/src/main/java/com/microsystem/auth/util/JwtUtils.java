package com.microsystem.auth.util;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTCreationException;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * JWT工具类
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class JwtUtils {

    /**
     * JWT密钥
     */
    @Value("${jwt.secret:microsystem-jwt-secret-key}")
    private String secret;

    /**
     * JWT过期时间(秒)
     */
    @Value("${jwt.expiration:86400}")
    private Long expiration;

    /**
     * JWT刷新时间(秒)
     */
    @Value("${jwt.refresh-expiration:604800}")
    private Long refreshExpiration;

    /**
     * JWT签发者
     */
    @Value("${jwt.issuer:microsystem}")
    private String issuer;

    /**
     * 生成访问Token
     */
    public String generateAccessToken(Long userId, String username) {
        return generateToken(userId, username, expiration);
    }

    /**
     * 生成刷新Token
     */
    public String generateRefreshToken(Long userId, String username) {
        return generateToken(userId, username, refreshExpiration);
    }

    /**
     * 生成Token
     */
    private String generateToken(Long userId, String username, Long expiration) {
        try {
            Algorithm algorithm = Algorithm.HMAC256(secret);
            Date now = new Date();
            Date expiryDate = new Date(now.getTime() + expiration * 1000);

            Map<String, Object> header = new HashMap<>();
            header.put("typ", "JWT");
            header.put("alg", "HS256");

            return JWT.create()
                    .withHeader(header)
                    .withIssuer(issuer)
                    .withSubject(username)
                    .withClaim("userId", userId)
                    .withClaim("username", username)
                    .withIssuedAt(now)
                    .withExpiresAt(expiryDate)
                    .sign(algorithm);
        } catch (JWTCreationException e) {
            log.error("JWT生成失败: {}", e.getMessage(), e);
            throw new RuntimeException("JWT生成失败", e);
        }
    }

    /**
     * 验证Token
     */
    public boolean validateToken(String token) {
        try {
            Algorithm algorithm = Algorithm.HMAC256(secret);
            JWTVerifier verifier = JWT.require(algorithm)
                    .withIssuer(issuer)
                    .build();
            verifier.verify(token);
            return true;
        } catch (JWTVerificationException e) {
            log.warn("JWT验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 解析Token
     */
    public DecodedJWT parseToken(String token) {
        try {
            Algorithm algorithm = Algorithm.HMAC256(secret);
            JWTVerifier verifier = JWT.require(algorithm)
                    .withIssuer(issuer)
                    .build();
            return verifier.verify(token);
        } catch (JWTVerificationException e) {
            log.error("JWT解析失败: {}", e.getMessage(), e);
            throw new RuntimeException("JWT解析失败", e);
        }
    }

    /**
     * 从Token中获取用户ID
     */
    public Long getUserIdFromToken(String token) {
        DecodedJWT decodedJWT = parseToken(token);
        return decodedJWT.getClaim("userId").asLong();
    }

    /**
     * 从Token中获取用户名
     */
    public String getUsernameFromToken(String token) {
        DecodedJWT decodedJWT = parseToken(token);
        return decodedJWT.getClaim("username").asString();
    }

    /**
     * 获取Token过期时间
     */
    public Date getExpirationDateFromToken(String token) {
        DecodedJWT decodedJWT = parseToken(token);
        return decodedJWT.getExpiresAt();
    }

    /**
     * 判断Token是否过期
     */
    public boolean isTokenExpired(String token) {
        Date expirationDate = getExpirationDateFromToken(token);
        return expirationDate.before(new Date());
    }

    /**
     * 刷新Token
     */
    public String refreshToken(String token) {
        if (validateToken(token) && !isTokenExpired(token)) {
            Long userId = getUserIdFromToken(token);
            String username = getUsernameFromToken(token);
            return generateAccessToken(userId, username);
        }
        throw new RuntimeException("Token无效或已过期，无法刷新");
    }
}
