# ShardingJDBC分库分表配置
spring:
  shardingsphere:
    # 数据源配置
    datasource:
      names: master,slave0,slave1
      
      # 主库配置
      master:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: *************************************************************************************************************************************************************
        username: microsystem
        password: microsystem123
        
      # 从库配置1
      slave0:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: *************************************************************************************************************************************************************
        username: microsystem
        password: microsystem123
        
      # 从库配置2
      slave1:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: *************************************************************************************************************************************************************
        username: microsystem
        password: microsystem123
    
    # 分片规则配置
    rules:
      # 读写分离规则
      readwrite-splitting:
        data-sources:
          business_rw:
            static-strategy:
              write-data-source-name: master
              read-data-source-names: slave0,slave1
            load-balancer-name: round_robin
        load-balancers:
          round_robin:
            type: ROUND_ROBIN
      
      # 分片规则
      sharding:
        tables:
          # 产品表分片规则
          biz_product:
            actual-data-nodes: business_rw.biz_product_$->{0..3}
            table-strategy:
              standard:
                sharding-column: id
                sharding-algorithm-name: product_table_inline
            key-generate-strategy:
              column: id
              key-generator-name: snowflake
          
          # 任务执行日志表分片规则
          job_execution_log:
            actual-data-nodes: business_rw.job_execution_log_$->{202401..202412}
            table-strategy:
              standard:
                sharding-column: create_time
                sharding-algorithm-name: log_table_by_month
            key-generate-strategy:
              column: id
              key-generator-name: snowflake
        
        # 分片算法配置
        sharding-algorithms:
          # 产品表按ID取模分片
          product_table_inline:
            type: INLINE
            props:
              algorithm-expression: biz_product_$->{id % 4}
          
          # 日志表按月分片
          log_table_by_month:
            type: INLINE
            props:
              algorithm-expression: job_execution_log_$->{create_time.format('yyyyMM')}
        
        # 主键生成策略
        key-generators:
          snowflake:
            type: SNOWFLAKE
            props:
              worker-id: 1
    
    # 属性配置
    props:
      # 显示SQL
      sql-show: true
      # 检查重复表
      check-duplicate-table-enabled: false
