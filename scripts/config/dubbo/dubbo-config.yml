# Dubbo配置文件
dubbo:
  # 应用配置
  application:
    name: ${spring.application.name}
    version: 1.0.0
    owner: MicroSystem Team
    organization: MicroSystem
    logger: slf4j
    
  # 注册中心配置
  registry:
    address: nacos://localhost:8848
    username: nacos
    password: nacos
    parameters:
      namespace: dev
      group: DEFAULT_GROUP
    timeout: 10000
    check: false
    
  # 协议配置
  protocol:
    name: dubbo
    port: -1  # 自动分配端口
    host: ${spring.cloud.client.ip-address:}
    threads: 200
    iothreads: 4
    accepts: 1000
    payload: 8388608  # 8MB
    buffer: 8192
    heartbeat: 60000
    serialization: hessian2
    
  # 提供者配置
  provider:
    timeout: 10000
    retries: 0
    loadbalance: roundrobin
    cluster: failfast
    filter: -exception
    validation: true
    cache: false
    
  # 消费者配置
  consumer:
    timeout: 10000
    retries: 2
    loadbalance: roundrobin
    cluster: failover
    check: false
    validation: true
    cache: false
    
  # 监控配置
  monitor:
    protocol: registry
    
  # 元数据配置
  metadata-report:
    address: nacos://localhost:8848
    username: nacos
    password: nacos
    parameters:
      namespace: dev
      group: DEFAULT_GROUP
      
  # 配置中心
  config-center:
    address: nacos://localhost:8848
    username: nacos
    password: nacos
    parameters:
      namespace: dev
      group: DEFAULT_GROUP
      
  # 扫描包配置
  scan:
    base-packages: com.microsystem.*.interfaces.facade
    
  # 服务配置
  services:
    # 用户服务接口
    user-service:
      interface: com.microsystem.auth.interfaces.facade.UserFacade
      version: 1.0.0
      group: auth
      timeout: 5000
      retries: 1
      
    # 产品服务接口
    product-service:
      interface: com.microsystem.business.interfaces.facade.ProductFacade
      version: 1.0.0
      group: business
      timeout: 8000
      retries: 2
      
    # 任务服务接口
    job-service:
      interface: com.microsystem.job.interfaces.facade.JobFacade
      version: 1.0.0
      group: job
      timeout: 10000
      retries: 0
      
  # 引用配置
  references:
    # 用户服务引用
    user-service-ref:
      interface: com.microsystem.auth.interfaces.facade.UserFacade
      version: 1.0.0
      group: auth
      timeout: 5000
      retries: 1
      check: false
      
    # 产品服务引用
    product-service-ref:
      interface: com.microsystem.business.interfaces.facade.ProductFacade
      version: 1.0.0
      group: business
      timeout: 8000
      retries: 2
      check: false
      
    # 任务服务引用
    job-service-ref:
      interface: com.microsystem.job.interfaces.facade.JobFacade
      version: 1.0.0
      group: job
      timeout: 10000
      retries: 0
      check: false

# 服务特定配置
---
# 认证服务Dubbo配置
spring:
  profiles: auth-service
dubbo:
  protocol:
    port: 20881
  provider:
    group: auth
    
---
# 业务服务Dubbo配置
spring:
  profiles: business-service
dubbo:
  protocol:
    port: 20882
  provider:
    group: business
    
---
# 任务服务Dubbo配置
spring:
  profiles: job-service
dubbo:
  protocol:
    port: 20883
  provider:
    group: job
