#!/bin/bash

# =============================================
# Nacos配置上传脚本
# 将配置文件上传到Nacos配置中心
# =============================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
SCRIPT_DIR=$(cd "$(dirname "$0")" && pwd)
NACOS_CONFIG_DIR="$SCRIPT_DIR/nacos"
NACOS_SERVER=${NACOS_SERVER:-"localhost:8848"}
NACOS_USERNAME=${NACOS_USERNAME:-"nacos"}
NACOS_PASSWORD=${NACOS_PASSWORD:-"nacos"}
NACOS_NAMESPACE=${NACOS_NAMESPACE:-""}

# 配置文件映射
declare -A CONFIG_FILES=(
    ["common-config.yml"]="common-config.yml:DEFAULT_GROUP"
    ["logging-config.yml"]="logging-config.yml:DEFAULT_GROUP"
    ["auth-service.yml"]="auth-service.yml:DEFAULT_GROUP"
    ["business-service.yml"]="business-service.yml:DEFAULT_GROUP"
    ["job-service.yml"]="job-service.yml:DEFAULT_GROUP"
    ["gateway-service.yml"]="gateway-service.yml:DEFAULT_GROUP"
)

# 检查Nacos服务状态
check_nacos_status() {
    log_info "检查Nacos服务状态..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "http://${NACOS_SERVER}/nacos/v1/ns/operator/metrics" > /dev/null; then
            log_info "Nacos服务已就绪"
            return 0
        fi
        
        log_warn "等待Nacos服务启动... (${attempt}/${max_attempts})"
        sleep 5
        attempt=$((attempt + 1))
    done
    
    log_error "Nacos服务启动超时"
    return 1
}

# 上传配置文件
upload_config() {
    local file_path=$1
    local data_id=$2
    local group=$3
    
    log_info "上传配置文件: $file_path -> $data_id ($group)"
    
    if [ ! -f "$file_path" ]; then
        log_error "配置文件不存在: $file_path"
        return 1
    fi
    
    # 构建请求参数
    local url="http://${NACOS_SERVER}/nacos/v1/cs/configs"
    local content=$(cat "$file_path")
    
    # 上传配置
    local response=$(curl -s -X POST "$url" \
        -d "dataId=${data_id}" \
        -d "group=${group}" \
        -d "content=${content}" \
        -d "tenant=${NACOS_NAMESPACE}" \
        --data-urlencode "username=${NACOS_USERNAME}" \
        --data-urlencode "password=${NACOS_PASSWORD}")
    
    if [ "$response" = "true" ]; then
        log_info "配置上传成功: $data_id"
        return 0
    else
        log_error "配置上传失败: $data_id, 响应: $response"
        return 1
    fi
}

# 验证配置
verify_config() {
    local data_id=$1
    local group=$2
    
    log_info "验证配置: $data_id ($group)"
    
    local url="http://${NACOS_SERVER}/nacos/v1/cs/configs"
    local response=$(curl -s -G "$url" \
        -d "dataId=${data_id}" \
        -d "group=${group}" \
        -d "tenant=${NACOS_NAMESPACE}")
    
    if [ -n "$response" ] && [ "$response" != "config data not exist" ]; then
        log_info "配置验证成功: $data_id"
        return 0
    else
        log_error "配置验证失败: $data_id"
        return 1
    fi
}

# 上传所有配置文件
upload_all_configs() {
    log_info "开始上传所有配置文件..."
    
    local success_count=0
    local total_count=${#CONFIG_FILES[@]}
    
    for file in "${!CONFIG_FILES[@]}"; do
        local file_path="$NACOS_CONFIG_DIR/$file"
        local config_info="${CONFIG_FILES[$file]}"
        local data_id=$(echo "$config_info" | cut -d':' -f1)
        local group=$(echo "$config_info" | cut -d':' -f2)
        
        if upload_config "$file_path" "$data_id" "$group"; then
            if verify_config "$data_id" "$group"; then
                success_count=$((success_count + 1))
            fi
        fi
        
        # 短暂延迟，避免请求过于频繁
        sleep 1
    done
    
    log_info "配置上传完成: $success_count/$total_count"
    
    if [ $success_count -eq $total_count ]; then
        return 0
    else
        return 1
    fi
}

# 显示配置列表
show_config_list() {
    log_info "Nacos配置列表:"
    
    local url="http://${NACOS_SERVER}/nacos/v1/cs/configs"
    local response=$(curl -s -G "$url" \
        -d "search=accurate" \
        -d "pageNo=1" \
        -d "pageSize=100" \
        -d "tenant=${NACOS_NAMESPACE}")
    
    if [ -n "$response" ]; then
        echo "$response" | jq -r '.pageItems[] | "\(.dataId) (\(.group))"' 2>/dev/null || echo "$response"
    else
        log_warn "无法获取配置列表"
    fi
}

# 备份现有配置
backup_configs() {
    log_info "备份现有配置..."
    
    local backup_dir="$SCRIPT_DIR/backup/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    for file in "${!CONFIG_FILES[@]}"; do
        local config_info="${CONFIG_FILES[$file]}"
        local data_id=$(echo "$config_info" | cut -d':' -f1)
        local group=$(echo "$config_info" | cut -d':' -f2)
        
        local url="http://${NACOS_SERVER}/nacos/v1/cs/configs"
        local content=$(curl -s -G "$url" \
            -d "dataId=${data_id}" \
            -d "group=${group}" \
            -d "tenant=${NACOS_NAMESPACE}")
        
        if [ -n "$content" ] && [ "$content" != "config data not exist" ]; then
            echo "$content" > "$backup_dir/${data_id}"
            log_info "备份配置: $data_id -> $backup_dir/${data_id}"
        fi
    done
    
    log_info "配置备份完成: $backup_dir"
}

# 主函数
main() {
    log_info "开始上传Nacos配置..."
    log_info "Nacos服务器: $NACOS_SERVER"
    log_info "命名空间: ${NACOS_NAMESPACE:-default}"
    
    # 检查Nacos服务状态
    if ! check_nacos_status; then
        exit 1
    fi
    
    # 备份现有配置
    backup_configs
    
    # 上传所有配置
    if upload_all_configs; then
        log_info "所有配置上传成功！"
        
        # 显示配置列表
        show_config_list
        
        log_info ""
        log_info "配置访问地址:"
        log_info "  Nacos控制台: http://${NACOS_SERVER}/nacos"
        log_info "  用户名/密码: ${NACOS_USERNAME}/${NACOS_PASSWORD}"
        
    else
        log_error "部分配置上传失败"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "Nacos配置上传脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示帮助信息"
    echo "  --server SERVER         Nacos服务器地址 (默认: localhost:8848)"
    echo "  --username USERNAME     Nacos用户名 (默认: nacos)"
    echo "  --password PASSWORD     Nacos密码 (默认: nacos)"
    echo "  --namespace NAMESPACE   Nacos命名空间 (默认: 空)"
    echo "  --backup-only           仅备份现有配置"
    echo "  --list                  显示配置列表"
    echo ""
    echo "环境变量:"
    echo "  NACOS_SERVER            Nacos服务器地址"
    echo "  NACOS_USERNAME          Nacos用户名"
    echo "  NACOS_PASSWORD          Nacos密码"
    echo "  NACOS_NAMESPACE         Nacos命名空间"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --server)
            NACOS_SERVER="$2"
            shift 2
            ;;
        --username)
            NACOS_USERNAME="$2"
            shift 2
            ;;
        --password)
            NACOS_PASSWORD="$2"
            shift 2
            ;;
        --namespace)
            NACOS_NAMESPACE="$2"
            shift 2
            ;;
        --backup-only)
            backup_configs
            exit 0
            ;;
        --list)
            show_config_list
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行主函数
main
