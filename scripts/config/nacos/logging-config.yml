# 日志配置文件
# 用于动态控制各个服务的日志级别和降级策略

# 全局日志配置
logging:
  # 日志降级配置
  degradation:
    # 是否启用日志降级模式
    enabled: false
    # 降级模式下的日志级别
    level: WARN
    # 降级触发条件
    triggers:
      # CPU使用率阈值（百分比）
      cpu-threshold: 80
      # 内存使用率阈值（百分比）
      memory-threshold: 85
      # QPS阈值
      qps-threshold: 1000
      # 错误率阈值（百分比）
      error-rate-threshold: 5
  
  # 正常模式下的日志级别
  normal:
    level: INFO
  
  # 业务关键操作的包路径
  business:
    critical:
      packages: com.microsystem
      # 关键操作列表
      operations:
        - login
        - register
        - payment
        - order
        - product_publish
        - job_execution
  
  # HTTP请求日志配置
  http:
    enabled: true
    # 是否记录请求体
    log-request-body: false
    # 是否记录响应体
    log-response-body: false
    # 最大请求体大小（字节）
    max-request-body-size: 1024
    # 最大响应体大小（字节）
    max-response-body-size: 1024
    # 排除的路径
    exclude-paths:
      - /actuator/**
      - /swagger-ui/**
      - /v3/api-docs/**
      - /favicon.ico
  
  # 性能监控配置
  performance:
    enabled: true
    # 慢方法阈值（毫秒）
    slow-method-threshold: 1000
    # 超慢方法阈值（毫秒）
    very-slow-method-threshold: 5000
    # 是否记录方法参数
    log-method-args: false
    # 是否记录返回值
    log-return-value: false
  
  # 异步日志配置
  async:
    # 队列大小
    queue-size: 2048
    # 丢弃阈值（0表示不丢弃）
    discarding-threshold: 0
    # 最大刷新时间（毫秒）
    max-flush-time: 2000
    # 是否永不阻塞
    never-block: true
    # 包含调用者信息
    include-caller-data: false
  
  # 日志文件配置
  file:
    # 单个文件最大大小
    max-size: 100MB
    # 保留天数
    max-history: 30
    # 总大小限制
    total-size-cap: 10GB
    # 压缩历史文件
    compress-history: true
  
  # 特定服务的日志级别配置
  services:
    # 认证服务
    auth-service:
      root-level: INFO
      package-levels:
        com.microsystem.auth.service: INFO
        com.microsystem.auth.controller: INFO
        com.microsystem.auth.security: DEBUG
      special-loggers:
        SECURITY: INFO
        BUSINESS_CRITICAL: INFO
    
    # 业务服务
    business-service:
      root-level: INFO
      package-levels:
        com.microsystem.business.application: INFO
        com.microsystem.business.domain: DEBUG
        com.microsystem.business.infrastructure: WARN
      special-loggers:
        BUSINESS_CRITICAL: INFO
        PERFORMANCE: INFO
        DATABASE: DEBUG
        MESSAGE_QUEUE: INFO
    
    # 任务服务
    job-service:
      root-level: INFO
      package-levels:
        com.microsystem.job.service: INFO
        com.microsystem.job.processor: DEBUG
        tech.powerjob: INFO
      special-loggers:
        BUSINESS_CRITICAL: INFO
        JOB_EXECUTION: INFO
        JOB_SCHEDULE: INFO
    
    # 网关服务
    gateway-service:
      root-level: INFO
      package-levels:
        com.microsystem.gateway.filter: INFO
        org.springframework.cloud.gateway: WARN
        reactor.netty: WARN
      special-loggers:
        HTTP_REQUEST: INFO
        HTTP_RESPONSE: INFO
        RATE_LIMIT: WARN
        GATEWAY_AUTH: INFO
        GATEWAY_ROUTE: DEBUG

# 环境特定配置
---
spring:
  config:
    activate:
      on-profile: dev

logging:
  degradation:
    enabled: false
  normal:
    level: DEBUG
  performance:
    enabled: true
    log-method-args: true
  http:
    log-request-body: true
    log-response-body: true

---
spring:
  config:
    activate:
      on-profile: test

logging:
  degradation:
    enabled: false
  normal:
    level: INFO
  performance:
    enabled: true
    log-method-args: false
  http:
    log-request-body: false
    log-response-body: false

---
spring:
  config:
    activate:
      on-profile: prod

logging:
  degradation:
    enabled: false
  normal:
    level: WARN
  performance:
    enabled: true
    log-method-args: false
    slow-method-threshold: 2000
  http:
    log-request-body: false
    log-response-body: false
  file:
    max-history: 60
    total-size-cap: 50GB
