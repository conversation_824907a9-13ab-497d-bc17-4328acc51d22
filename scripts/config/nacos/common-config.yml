# 公共配置文件 - 在Nacos中配置
# Data ID: common-config.yml
# Group: DEFAULT_GROUP

# 公共数据源配置
spring:
  datasource:
    druid:
      # 连接池配置
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall,slf4j
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      
      # Web监控配置
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        login-username: admin
        login-password: admin123

# 公共Redis配置
  data:
    redis:
      timeout: 10000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0

# MyBatis Plus公共配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  global-config:
    db-config:
      id-type: ASSIGN_ID
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 公共日志配置
logging:
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-}] [%X{userId:-}] %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-}] [%X{userId:-}] %logger{50} - %msg%n"

  # 日志降级配置
  degradation:
    # 是否启用日志降级
    enabled: false
    # 降级模式下的日志级别
    level: WARN

  # 正常模式下的日志级别
  normal:
    level: INFO

  # 业务关键操作的包路径
  business:
    critical:
      packages: com.microsystem

  # HTTP请求日志开关
  http:
    enabled: true

  # 异步日志配置
  async:
    # 队列大小
    queue-size: 2048
    # 丢弃阈值
    discarding-threshold: 0
    # 最大刷新时间
    max-flush-time: 2000
    # 是否永不阻塞
    never-block: true

  # 日志文件配置
  file:
    # 单个文件最大大小
    max-size: 100MB
    # 保留天数
    max-history: 30
    # 总大小限制
    total-size-cap: 10GB

# 公共Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always

# 公共Swagger配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  info:
    contact:
      name: MicroSystem Team
      email: <EMAIL>

# 公共业务配置
microsystem:
  # 系统配置
  system:
    name: MicroSystem
    version: 1.0.0
    description: Production-grade microservice architecture system
  
  # 安全配置
  security:
    # JWT配置
    jwt:
      secret: microsystem-jwt-secret-key-2024
      expiration: 86400  # 24小时
      refresh-expiration: 604800  # 7天
      issuer: microsystem
    
    # 密码策略
    password:
      min-length: 8
      max-length: 20
      require-uppercase: true
      require-lowercase: true
      require-digit: true
      require-special-char: false
  
  # 文件上传配置
  file:
    upload:
      max-size: 10MB
      allowed-types: jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx
      base-path: /data/uploads
  
  # 缓存配置
  cache:
    default-ttl: 3600  # 默认1小时
    max-size: 10000    # 最大缓存条目数
  
  # 限流配置
  rate-limit:
    enabled: true
    default-limit: 100  # 每分钟100次
    window-size: 60     # 时间窗口60秒
