# Pulsar配置文件
pulsar:
  # 服务地址
  service-url: pulsar://localhost:6650
  admin-url: http://localhost:8080
  
  # 租户和命名空间
  tenant: public
  namespace: microsystem
  
  # 生产者配置
  producer:
    # 默认主题
    default-topic: microsystem-events
    # 发送超时时间(毫秒)
    send-timeout: 30000
    # 批量发送
    batching-enabled: true
    # 批量大小
    batching-max-messages: 1000
    # 批量延迟(毫秒)
    batching-max-publish-delay: 10
    # 压缩类型
    compression-type: LZ4
    # 重试次数
    max-pending-messages: 1000
  
  # 消费者配置
  consumer:
    # 订阅类型
    subscription-type: Shared
    # 确认超时时间(毫秒)
    ack-timeout: 30000
    # 接收队列大小
    receiver-queue-size: 1000
    # 消费者名称前缀
    consumer-name-prefix: microsystem-consumer
    # 重试策略
    retry:
      # 最大重试次数
      max-retry-count: 3
      # 重试延迟(毫秒)
      retry-delay: 1000
      # 死信主题
      dead-letter-topic: microsystem-dlq
  
  # 主题配置
  topics:
    # 用户事件主题
    user-events:
      name: persistent://public/microsystem/user-events
      partitions: 3
      retention-policy:
        retention-time: 7d
        retention-size: 1GB
    
    # 产品事件主题
    product-events:
      name: persistent://public/microsystem/product-events
      partitions: 4
      retention-policy:
        retention-time: 30d
        retention-size: 5GB
    
    # 任务事件主题
    job-events:
      name: persistent://public/microsystem/job-events
      partitions: 2
      retention-policy:
        retention-time: 90d
        retention-size: 10GB
    
    # 系统通知主题
    system-notifications:
      name: persistent://public/microsystem/system-notifications
      partitions: 1
      retention-policy:
        retention-time: 365d
        retention-size: 2GB
  
  # 订阅配置
  subscriptions:
    # 用户事件订阅
    user-events-subscription:
      topic: persistent://public/microsystem/user-events
      subscription-name: user-events-handler
      subscription-type: Shared
      initial-position: Latest
    
    # 产品事件订阅
    product-events-subscription:
      topic: persistent://public/microsystem/product-events
      subscription-name: product-events-handler
      subscription-type: Shared
      initial-position: Latest
    
    # 任务事件订阅
    job-events-subscription:
      topic: persistent://public/microsystem/job-events
      subscription-name: job-events-handler
      subscription-type: Exclusive
      initial-position: Latest
    
    # 系统通知订阅
    notification-subscription:
      topic: persistent://public/microsystem/system-notifications
      subscription-name: notification-handler
      subscription-type: Shared
      initial-position: Latest

# Spring Boot集成配置
spring:
  pulsar:
    client:
      service-url: ${pulsar.service-url}
      connection-timeout: 30s
      operation-timeout: 30s
      lookup-timeout: 30s
      
    producer:
      topic-name: ${pulsar.producer.default-topic}
      send-timeout: ${pulsar.producer.send-timeout}ms
      batching-enabled: ${pulsar.producer.batching-enabled}
      
    consumer:
      subscription:
        name: default-subscription
        type: shared
      topics:
        - ${pulsar.producer.default-topic}
