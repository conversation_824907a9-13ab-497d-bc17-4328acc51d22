# Redis集群配置文件模板
# 端口配置
port 7000

# 绑定地址
bind 127.0.0.1

# 启用集群模式
cluster-enabled yes

# 集群配置文件
cluster-config-file nodes-7000.conf

# 集群节点超时时间
cluster-node-timeout 15000

# 启用AOF持久化
appendonly yes

# AOF文件名
appendfilename "appendonly-7000.aof"

# 数据目录
dir /var/lib/redis/7000

# 日志文件
logfile /var/log/redis/redis-7000.log

# 日志级别
loglevel notice

# 守护进程模式
daemonize yes

# PID文件
pidfile /var/run/redis/redis-7000.pid

# 最大内存
maxmemory 1gb

# 内存淘汰策略
maxmemory-policy allkeys-lru

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128

# 客户端超时时间
timeout 300

# TCP keepalive
tcp-keepalive 300

# 数据库数量
databases 16

# 保存策略
save 900 1
save 300 10
save 60 10000

# 压缩字符串对象
rdbcompression yes

# 校验RDB文件
rdbchecksum yes

# RDB文件名
dbfilename dump-7000.rdb

# 主从复制配置
# replica-serve-stale-data yes
# replica-read-only yes

# 安全配置
# requirepass your_password_here

# 重命名危险命令
# rename-command FLUSHDB ""
# rename-command FLUSHALL ""
# rename-command KEYS ""
# rename-command CONFIG ""

# 客户端连接数限制
maxclients 10000

# 内存使用报告
# maxmemory-samples 5

# 延迟监控
latency-monitor-threshold 100
