#!/bin/bash

# =============================================
# 微服务架构系统清理脚本
# =============================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 配置变量
PROJECT_ROOT=$(cd "$(dirname "$0")/../.." && pwd)
DOCKER_DIR="$PROJECT_ROOT/docker"
COMPOSE_FILE="$DOCKER_DIR/docker-compose.yml"

# 清理选项
STOP_SERVICES=${STOP_SERVICES:-"true"}
REMOVE_CONTAINERS=${REMOVE_CONTAINERS:-"true"}
REMOVE_IMAGES=${REMOVE_IMAGES:-"false"}
REMOVE_VOLUMES=${REMOVE_VOLUMES:-"false"}
REMOVE_NETWORKS=${REMOVE_NETWORKS:-"false"}
PRUNE_SYSTEM=${PRUNE_SYSTEM:-"false"}

# 检查环境
check_environment() {
    log_info "检查环境..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose未安装"
        exit 1
    fi
    
    # 检查文件
    if [ ! -f "$COMPOSE_FILE" ]; then
        log_error "Docker Compose文件不存在: $COMPOSE_FILE"
        exit 1
    fi
    
    log_info "环境检查通过"
}

# 停止服务
stop_services() {
    if [ "$STOP_SERVICES" = "true" ]; then
        log_info "停止所有服务..."
        
        cd "$DOCKER_DIR"
        
        # 优雅停止服务
        if docker-compose ps -q | grep -q .; then
            docker-compose stop
            log_info "服务停止完成"
        else
            log_info "没有运行中的服务"
        fi
    else
        log_info "跳过停止服务"
    fi
}

# 删除容器
remove_containers() {
    if [ "$REMOVE_CONTAINERS" = "true" ]; then
        log_info "删除容器..."
        
        cd "$DOCKER_DIR"
        
        # 删除所有容器
        if docker-compose ps -a -q | grep -q .; then
            docker-compose rm -f
            log_info "容器删除完成"
        else
            log_info "没有容器需要删除"
        fi
        
        # 删除孤立容器
        local orphan_containers=$(docker ps -a --filter "name=microsystem-" -q)
        if [ -n "$orphan_containers" ]; then
            log_info "删除孤立容器..."
            docker rm -f $orphan_containers
        fi
    else
        log_info "跳过删除容器"
    fi
}

# 删除镜像
remove_images() {
    if [ "$REMOVE_IMAGES" = "true" ]; then
        log_info "删除镜像..."
        
        # 删除项目相关镜像
        local images=$(docker images --filter "reference=microsystem/*" -q)
        if [ -n "$images" ]; then
            log_info "删除项目镜像..."
            docker rmi -f $images
        fi
        
        # 删除未使用的镜像
        log_info "删除未使用的镜像..."
        docker image prune -f
        
        log_info "镜像删除完成"
    else
        log_info "跳过删除镜像"
    fi
}

# 删除数据卷
remove_volumes() {
    if [ "$REMOVE_VOLUMES" = "true" ]; then
        log_warn "删除数据卷将导致数据丢失！"
        read -p "确认删除数据卷? (y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            log_info "删除数据卷..."
            
            cd "$DOCKER_DIR"
            
            # 删除项目数据卷
            docker-compose down -v
            
            # 删除孤立数据卷
            local volumes=$(docker volume ls --filter "name=microsystem" -q)
            if [ -n "$volumes" ]; then
                docker volume rm $volumes
            fi
            
            # 删除未使用的数据卷
            docker volume prune -f
            
            log_info "数据卷删除完成"
        else
            log_info "跳过删除数据卷"
        fi
    else
        log_info "跳过删除数据卷"
    fi
}

# 删除网络
remove_networks() {
    if [ "$REMOVE_NETWORKS" = "true" ]; then
        log_info "删除网络..."
        
        # 删除项目网络
        if docker network ls | grep -q "microsystem-network"; then
            docker network rm microsystem-network 2>/dev/null || true
        fi
        
        # 删除未使用的网络
        docker network prune -f
        
        log_info "网络删除完成"
    else
        log_info "跳过删除网络"
    fi
}

# 系统清理
prune_system() {
    if [ "$PRUNE_SYSTEM" = "true" ]; then
        log_warn "系统清理将删除所有未使用的Docker资源！"
        read -p "确认执行系统清理? (y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            log_info "执行系统清理..."
            
            # 清理所有未使用的资源
            docker system prune -a -f --volumes
            
            log_info "系统清理完成"
        else
            log_info "跳过系统清理"
        fi
    else
        log_info "跳过系统清理"
    fi
}

# 显示清理结果
show_cleanup_result() {
    log_info "清理结果:"
    
    # 显示剩余容器
    local containers=$(docker ps -a --filter "name=microsystem-" --format "table {{.Names}}\t{{.Status}}")
    if [ -n "$containers" ] && [ "$containers" != "NAMES	STATUS" ]; then
        log_info "剩余容器:"
        echo "$containers"
    else
        log_info "✓ 没有剩余容器"
    fi
    
    # 显示剩余镜像
    local images=$(docker images --filter "reference=microsystem/*" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}")
    if [ -n "$images" ] && [ "$images" != "REPOSITORY	TAG	SIZE" ]; then
        log_info "剩余镜像:"
        echo "$images"
    else
        log_info "✓ 没有剩余镜像"
    fi
    
    # 显示剩余数据卷
    local volumes=$(docker volume ls --filter "name=microsystem" --format "table {{.Name}}")
    if [ -n "$volumes" ] && [ "$volumes" != "NAME" ]; then
        log_info "剩余数据卷:"
        echo "$volumes"
    else
        log_info "✓ 没有剩余数据卷"
    fi
    
    # 显示磁盘使用情况
    log_info "Docker磁盘使用情况:"
    docker system df
}

# 主函数
main() {
    log_info "开始清理微服务架构系统..."
    
    # 环境检查
    check_environment
    
    # 执行清理步骤
    stop_services
    remove_containers
    remove_images
    remove_volumes
    remove_networks
    prune_system
    
    # 显示清理结果
    show_cleanup_result
    
    log_info "微服务架构系统清理完成！"
}

# 显示帮助信息
show_help() {
    echo "微服务架构系统清理脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示帮助信息"
    echo "  --stop-only             仅停止服务"
    echo "  --remove-containers     删除容器"
    echo "  --remove-images         删除镜像"
    echo "  --remove-volumes        删除数据卷 (危险操作)"
    echo "  --remove-networks       删除网络"
    echo "  --prune-system          系统清理 (危险操作)"
    echo "  --all                   删除所有资源 (危险操作)"
    echo ""
    echo "环境变量:"
    echo "  STOP_SERVICES           是否停止服务"
    echo "  REMOVE_CONTAINERS       是否删除容器"
    echo "  REMOVE_IMAGES           是否删除镜像"
    echo "  REMOVE_VOLUMES          是否删除数据卷"
    echo "  REMOVE_NETWORKS         是否删除网络"
    echo "  PRUNE_SYSTEM            是否执行系统清理"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --stop-only)
            STOP_SERVICES="true"
            REMOVE_CONTAINERS="false"
            REMOVE_IMAGES="false"
            REMOVE_VOLUMES="false"
            REMOVE_NETWORKS="false"
            PRUNE_SYSTEM="false"
            shift
            ;;
        --remove-containers)
            REMOVE_CONTAINERS="true"
            shift
            ;;
        --remove-images)
            REMOVE_IMAGES="true"
            shift
            ;;
        --remove-volumes)
            REMOVE_VOLUMES="true"
            shift
            ;;
        --remove-networks)
            REMOVE_NETWORKS="true"
            shift
            ;;
        --prune-system)
            PRUNE_SYSTEM="true"
            shift
            ;;
        --all)
            STOP_SERVICES="true"
            REMOVE_CONTAINERS="true"
            REMOVE_IMAGES="true"
            REMOVE_VOLUMES="true"
            REMOVE_NETWORKS="true"
            PRUNE_SYSTEM="true"
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行主函数
main
