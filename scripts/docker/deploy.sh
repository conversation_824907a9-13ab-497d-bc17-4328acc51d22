#!/bin/bash

# =============================================
# 微服务架构系统一键部署脚本
# =============================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 配置变量
PROJECT_ROOT=$(cd "$(dirname "$0")/../.." && pwd)
DOCKER_DIR="$PROJECT_ROOT/docker"
ENV_FILE="$DOCKER_DIR/.env"
COMPOSE_FILE="$DOCKER_DIR/docker-compose.yml"
OVERRIDE_FILE="$DOCKER_DIR/docker-compose.override.yml"

# 部署环境
DEPLOY_ENV=${DEPLOY_ENV:-"dev"}
BUILD_IMAGES=${BUILD_IMAGES:-"true"}
WAIT_TIMEOUT=${WAIT_TIMEOUT:-"300"}

# 检查环境
check_environment() {
    log_info "检查部署环境..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    # 检查Docker服务
    if ! docker info &> /dev/null; then
        log_error "Docker服务未启动，请启动Docker服务"
        exit 1
    fi
    
    # 检查文件
    if [ ! -f "$COMPOSE_FILE" ]; then
        log_error "Docker Compose文件不存在: $COMPOSE_FILE"
        exit 1
    fi
    
    log_info "环境检查通过"
}

# 构建镜像
build_images() {
    if [ "$BUILD_IMAGES" = "true" ]; then
        log_info "构建Docker镜像..."
        
        local build_script="$PROJECT_ROOT/scripts/docker/build-images.sh"
        if [ -f "$build_script" ]; then
            chmod +x "$build_script"
            "$build_script"
        else
            log_error "构建脚本不存在: $build_script"
            exit 1
        fi
        
        log_info "镜像构建完成"
    else
        log_info "跳过镜像构建"
    fi
}

# 创建网络和数据卷
setup_docker_resources() {
    log_info "创建Docker网络和数据卷..."
    
    # 创建网络
    if ! docker network ls | grep -q "microsystem-network"; then
        docker network create microsystem-network --driver bridge --subnet=**********/16
        log_info "创建网络: microsystem-network"
    fi
    
    # 创建数据卷
    local volumes=("mysql-data" "redis-data" "nacos-data" "pulsar-data" "powerjob-data")
    for volume in "${volumes[@]}"; do
        if ! docker volume ls | grep -q "$volume"; then
            docker volume create "$volume"
            log_info "创建数据卷: $volume"
        fi
    done
}

# 启动基础设施服务
start_infrastructure() {
    log_info "启动基础设施服务..."
    
    cd "$DOCKER_DIR"
    
    # 启动MySQL
    log_info "启动MySQL..."
    docker-compose up -d mysql
    wait_for_service "mysql" "3306" "MySQL"
    
    # 启动Redis
    log_info "启动Redis..."
    docker-compose up -d redis
    wait_for_service "redis" "6379" "Redis"
    
    # 启动Nacos
    log_info "启动Nacos..."
    docker-compose up -d nacos
    wait_for_service "nacos" "8848" "Nacos"
    
    # 启动Sentinel
    log_info "启动Sentinel..."
    docker-compose up -d sentinel
    
    # 启动Pulsar
    log_info "启动Pulsar..."
    docker-compose up -d pulsar
    wait_for_service "pulsar" "8090" "Pulsar"
    
    # 启动PowerJob
    log_info "启动PowerJob..."
    docker-compose up -d powerjob
    
    log_info "基础设施服务启动完成"
}

# 启动微服务
start_microservices() {
    log_info "启动微服务..."
    
    cd "$DOCKER_DIR"
    
    # 按顺序启动微服务
    local services=("auth-service" "business-service" "job-service" "gateway-service")
    
    for service in "${services[@]}"; do
        log_info "启动 $service..."
        docker-compose up -d "$service"
        
        # 等待服务健康检查通过
        wait_for_healthy_service "$service"
    done
    
    log_info "微服务启动完成"
}

# 等待服务端口可用
wait_for_service() {
    local service=$1
    local port=$2
    local name=$3
    local timeout=${4:-60}
    
    log_info "等待 $name 服务启动..."
    
    local count=0
    while [ $count -lt $timeout ]; do
        if docker exec "microsystem-$service" nc -z localhost "$port" 2>/dev/null; then
            log_info "$name 服务已启动"
            return 0
        fi
        
        sleep 2
        count=$((count + 2))
        
        if [ $((count % 10)) -eq 0 ]; then
            log_debug "等待 $name 服务启动... ($count/${timeout}s)"
        fi
    done
    
    log_error "$name 服务启动超时"
    return 1
}

# 等待服务健康检查通过
wait_for_healthy_service() {
    local service=$1
    local timeout=${2:-120}
    
    log_info "等待 $service 健康检查通过..."
    
    local count=0
    while [ $count -lt $timeout ]; do
        local health_status=$(docker inspect --format='{{.State.Health.Status}}' "microsystem-$service" 2>/dev/null || echo "unknown")
        
        if [ "$health_status" = "healthy" ]; then
            log_info "$service 健康检查通过"
            return 0
        elif [ "$health_status" = "unhealthy" ]; then
            log_error "$service 健康检查失败"
            return 1
        fi
        
        sleep 5
        count=$((count + 5))
        
        if [ $((count % 15)) -eq 0 ]; then
            log_debug "等待 $service 健康检查... ($count/${timeout}s)"
        fi
    done
    
    log_error "$service 健康检查超时"
    return 1
}

# 验证部署
verify_deployment() {
    log_info "验证部署状态..."
    
    cd "$DOCKER_DIR"
    
    # 检查所有服务状态
    local all_healthy=true
    local services=$(docker-compose ps --services)
    
    for service in $services; do
        local status=$(docker-compose ps -q "$service" | xargs docker inspect --format='{{.State.Status}}' 2>/dev/null || echo "not_found")
        local health=$(docker-compose ps -q "$service" | xargs docker inspect --format='{{.State.Health.Status}}' 2>/dev/null || echo "none")
        
        if [ "$status" = "running" ]; then
            if [ "$health" = "healthy" ] || [ "$health" = "none" ]; then
                log_info "✓ $service: $status ($health)"
            else
                log_error "✗ $service: $status ($health)"
                all_healthy=false
            fi
        else
            log_error "✗ $service: $status"
            all_healthy=false
        fi
    done
    
    if [ "$all_healthy" = true ]; then
        log_info "所有服务部署成功"
        show_access_info
    else
        log_error "部分服务部署失败"
        return 1
    fi
}

# 显示访问信息
show_access_info() {
    log_info "系统访问信息:"
    log_info "  网关服务: http://localhost:8080"
    log_info "  认证服务: http://localhost:8081"
    log_info "  业务服务: http://localhost:8082"
    log_info "  任务服务: http://localhost:8083"
    log_info ""
    log_info "管理控制台:"
    log_info "  Nacos: http://localhost:8848/nacos (nacos/nacos)"
    log_info "  Sentinel: http://localhost:8858 (sentinel/sentinel)"
    log_info "  Pulsar: http://localhost:8090"
    log_info "  PowerJob: http://localhost:7700 (admin/123456)"
    log_info ""
    log_info "API文档:"
    log_info "  认证服务: http://localhost:8081/swagger-ui.html"
    log_info "  业务服务: http://localhost:8082/swagger-ui.html"
    log_info "  任务服务: http://localhost:8083/swagger-ui.html"
}

# 主函数
main() {
    log_info "开始部署微服务架构系统..."
    log_info "部署环境: $DEPLOY_ENV"
    
    # 环境检查
    check_environment
    
    # 构建镜像
    build_images
    
    # 设置Docker资源
    setup_docker_resources
    
    # 启动基础设施
    start_infrastructure
    
    # 等待基础设施稳定
    sleep 10
    
    # 启动微服务
    start_microservices
    
    # 验证部署
    verify_deployment
    
    log_info "微服务架构系统部署完成！"
}

# 显示帮助信息
show_help() {
    echo "微服务架构系统一键部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示帮助信息"
    echo "  --env ENV               部署环境 (dev/test/prod, 默认: dev)"
    echo "  --no-build              跳过镜像构建"
    echo "  --timeout SECONDS       等待超时时间 (默认: 300)"
    echo ""
    echo "环境变量:"
    echo "  DEPLOY_ENV              部署环境"
    echo "  BUILD_IMAGES            是否构建镜像"
    echo "  WAIT_TIMEOUT            等待超时时间"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --env)
            DEPLOY_ENV="$2"
            shift 2
            ;;
        --no-build)
            BUILD_IMAGES="false"
            shift
            ;;
        --timeout)
            WAIT_TIMEOUT="$2"
            shift 2
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行主函数
main
