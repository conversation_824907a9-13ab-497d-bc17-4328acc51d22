#!/bin/bash

# =============================================
# 微服务架构系统Docker镜像构建脚本
# =============================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 配置变量
PROJECT_ROOT=$(cd "$(dirname "$0")/../.." && pwd)
DOCKER_REGISTRY=${DOCKER_REGISTRY:-"microsystem"}
IMAGE_TAG=${IMAGE_TAG:-"1.0.0"}
BUILD_PARALLEL=${BUILD_PARALLEL:-"true"}

# 服务列表
SERVICES=("auth-service" "business-service" "job-service" "gateway-service")

# 检查Docker环境
check_docker() {
    log_info "检查Docker环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker服务未启动，请启动Docker服务"
        exit 1
    fi
    
    log_info "Docker环境检查通过"
}

# 检查Maven环境
check_maven() {
    log_info "检查Maven环境..."
    
    if ! command -v mvn &> /dev/null; then
        log_error "Maven未安装，请先安装Maven"
        exit 1
    fi
    
    log_info "Maven环境检查通过"
}

# 清理旧的构建产物
clean_build() {
    log_info "清理旧的构建产物..."
    
    cd "$PROJECT_ROOT"
    mvn clean -q
    
    # 清理Docker镜像
    for service in "${SERVICES[@]}"; do
        if docker images | grep -q "${DOCKER_REGISTRY}/${service}"; then
            log_debug "删除旧镜像: ${DOCKER_REGISTRY}/${service}"
            docker rmi "${DOCKER_REGISTRY}/${service}:${IMAGE_TAG}" 2>/dev/null || true
        fi
    done
    
    log_info "清理完成"
}

# 构建Maven项目
build_maven_project() {
    log_info "构建Maven项目..."
    
    cd "$PROJECT_ROOT"
    
    # 跳过测试构建，加快构建速度
    mvn package -DskipTests -q
    
    if [ $? -eq 0 ]; then
        log_info "Maven项目构建成功"
    else
        log_error "Maven项目构建失败"
        exit 1
    fi
}

# 构建单个服务的Docker镜像
build_service_image() {
    local service=$1
    local service_dir="$PROJECT_ROOT/$service"
    
    log_info "构建 $service 镜像..."
    
    if [ ! -d "$service_dir" ]; then
        log_error "服务目录不存在: $service_dir"
        return 1
    fi
    
    if [ ! -f "$service_dir/Dockerfile" ]; then
        log_error "Dockerfile不存在: $service_dir/Dockerfile"
        return 1
    fi
    
    cd "$service_dir"
    
    # 构建Docker镜像
    docker build \
        --tag "${DOCKER_REGISTRY}/${service}:${IMAGE_TAG}" \
        --tag "${DOCKER_REGISTRY}/${service}:latest" \
        --build-arg BUILD_DATE="$(date -u +'%Y-%m-%dT%H:%M:%SZ')" \
        --build-arg VCS_REF="$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')" \
        .
    
    if [ $? -eq 0 ]; then
        log_info "$service 镜像构建成功"
        return 0
    else
        log_error "$service 镜像构建失败"
        return 1
    fi
}

# 并行构建所有服务镜像
build_all_images_parallel() {
    log_info "并行构建所有服务镜像..."
    
    local pids=()
    local failed_services=()
    
    # 启动并行构建
    for service in "${SERVICES[@]}"; do
        build_service_image "$service" &
        pids+=($!)
    done
    
    # 等待所有构建完成
    for i in "${!pids[@]}"; do
        local pid=${pids[$i]}
        local service=${SERVICES[$i]}
        
        if wait $pid; then
            log_info "$service 构建完成"
        else
            log_error "$service 构建失败"
            failed_services+=("$service")
        fi
    done
    
    # 检查是否有失败的服务
    if [ ${#failed_services[@]} -gt 0 ]; then
        log_error "以下服务构建失败: ${failed_services[*]}"
        exit 1
    fi
    
    log_info "所有服务镜像构建完成"
}

# 串行构建所有服务镜像
build_all_images_serial() {
    log_info "串行构建所有服务镜像..."
    
    for service in "${SERVICES[@]}"; do
        if ! build_service_image "$service"; then
            log_error "服务 $service 构建失败，停止构建"
            exit 1
        fi
    done
    
    log_info "所有服务镜像构建完成"
}

# 验证构建结果
verify_images() {
    log_info "验证构建结果..."
    
    local all_success=true
    
    for service in "${SERVICES[@]}"; do
        if docker images | grep -q "${DOCKER_REGISTRY}/${service}.*${IMAGE_TAG}"; then
            local image_size=$(docker images --format "table {{.Size}}" "${DOCKER_REGISTRY}/${service}:${IMAGE_TAG}" | tail -n 1)
            log_info "✓ $service:${IMAGE_TAG} - $image_size"
        else
            log_error "✗ $service:${IMAGE_TAG} 镜像不存在"
            all_success=false
        fi
    done
    
    if [ "$all_success" = true ]; then
        log_info "所有镜像验证通过"
    else
        log_error "部分镜像验证失败"
        exit 1
    fi
}

# 推送镜像到仓库
push_images() {
    if [ "$PUSH_IMAGES" = "true" ]; then
        log_info "推送镜像到仓库..."
        
        for service in "${SERVICES[@]}"; do
            log_info "推送 ${DOCKER_REGISTRY}/${service}:${IMAGE_TAG}"
            docker push "${DOCKER_REGISTRY}/${service}:${IMAGE_TAG}"
            docker push "${DOCKER_REGISTRY}/${service}:latest"
        done
        
        log_info "镜像推送完成"
    fi
}

# 显示构建信息
show_build_info() {
    log_info "构建信息:"
    log_info "  项目根目录: $PROJECT_ROOT"
    log_info "  Docker仓库: $DOCKER_REGISTRY"
    log_info "  镜像标签: $IMAGE_TAG"
    log_info "  并行构建: $BUILD_PARALLEL"
    log_info "  服务列表: ${SERVICES[*]}"
}

# 主函数
main() {
    log_info "开始构建微服务架构系统Docker镜像..."
    
    show_build_info
    
    # 环境检查
    check_docker
    check_maven
    
    # 清理旧构建
    clean_build
    
    # 构建Maven项目
    build_maven_project
    
    # 构建Docker镜像
    if [ "$BUILD_PARALLEL" = "true" ]; then
        build_all_images_parallel
    else
        build_all_images_serial
    fi
    
    # 验证构建结果
    verify_images
    
    # 推送镜像
    push_images
    
    log_info "Docker镜像构建完成！"
    log_info ""
    log_info "使用以下命令启动系统:"
    log_info "  cd docker && docker-compose up -d"
}

# 显示帮助信息
show_help() {
    echo "微服务架构系统Docker镜像构建脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示帮助信息"
    echo "  --registry REGISTRY     Docker仓库地址 (默认: microsystem)"
    echo "  --tag TAG               镜像标签 (默认: 1.0.0)"
    echo "  --parallel              并行构建 (默认: true)"
    echo "  --serial                串行构建"
    echo "  --push                  构建完成后推送镜像"
    echo "  --clean-only            仅清理，不构建"
    echo ""
    echo "环境变量:"
    echo "  DOCKER_REGISTRY         Docker仓库地址"
    echo "  IMAGE_TAG               镜像标签"
    echo "  BUILD_PARALLEL          是否并行构建"
    echo "  PUSH_IMAGES             是否推送镜像"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --registry)
            DOCKER_REGISTRY="$2"
            shift 2
            ;;
        --tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        --parallel)
            BUILD_PARALLEL="true"
            shift
            ;;
        --serial)
            BUILD_PARALLEL="false"
            shift
            ;;
        --push)
            PUSH_IMAGES="true"
            shift
            ;;
        --clean-only)
            check_docker
            clean_build
            log_info "清理完成"
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行主函数
main
