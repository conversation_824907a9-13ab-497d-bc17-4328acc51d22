-- =============================================
-- 微服务架构系统初始化数据脚本
-- =============================================

-- =============================================
-- 认证服务初始化数据
-- =============================================
USE `microsystem_auth`;

-- 初始化用户数据
INSERT INTO `sys_user` (`id`, `username`, `password`, `nickname`, `real_name`, `email`, `mobile`, `status`, `create_by`) VALUES
(1, 'admin', '$2a$10$7JB720yubVSOfvVWdBYoOeymNWjQy2YjmpNwabkk.OgdwQBOqMzFm', '系统管理员', '管理员', '<EMAIL>', '13800138000', 1, 1),
(2, 'user', '$2a$10$7JB720yubVSOfvVWdBYoOeymNWjQy2YjmpNwabkk.OgdwQBOqMzFm', '普通用户', '用户', '<EMAIL>', '13800138001', 1, 1);

-- 初始化角色数据
INSERT INTO `sys_role` (`id`, `role_code`, `role_name`, `description`, `sort_order`, `status`, `create_by`) VALUES
(1, 'ADMIN', '系统管理员', '系统管理员角色，拥有所有权限', 1, 1, 1),
(2, 'USER', '普通用户', '普通用户角色，拥有基本权限', 2, 1, 1),
(3, 'GUEST', '访客', '访客角色，只有查看权限', 3, 1, 1);

-- 初始化权限数据
INSERT INTO `sys_permission` (`id`, `permission_code`, `permission_name`, `permission_type`, `parent_id`, `path`, `sort_order`, `status`, `create_by`) VALUES
-- 系统管理
(1, 'SYSTEM', '系统管理', 1, 0, '/system', 1, 1, 1),
(2, 'SYSTEM:USER', '用户管理', 1, 1, '/system/user', 1, 1, 1),
(3, 'SYSTEM:USER:LIST', '用户列表', 3, 2, '/api/users', 1, 1, 1),
(4, 'SYSTEM:USER:CREATE', '创建用户', 3, 2, '/api/users', 2, 1, 1),
(5, 'SYSTEM:USER:UPDATE', '更新用户', 3, 2, '/api/users/*', 3, 1, 1),
(6, 'SYSTEM:USER:DELETE', '删除用户', 3, 2, '/api/users/*', 4, 1, 1),
(7, 'SYSTEM:ROLE', '角色管理', 1, 1, '/system/role', 2, 1, 1),
(8, 'SYSTEM:ROLE:LIST', '角色列表', 3, 7, '/api/roles', 1, 1, 1),
(9, 'SYSTEM:ROLE:CREATE', '创建角色', 3, 7, '/api/roles', 2, 1, 1),
(10, 'SYSTEM:ROLE:UPDATE', '更新角色', 3, 7, '/api/roles/*', 3, 1, 1),
(11, 'SYSTEM:ROLE:DELETE', '删除角色', 3, 7, '/api/roles/*', 4, 1, 1),

-- 业务管理
(20, 'BUSINESS', '业务管理', 1, 0, '/business', 2, 1, 1),
(21, 'BUSINESS:PRODUCT', '产品管理', 1, 20, '/business/product', 1, 1, 1),
(22, 'BUSINESS:PRODUCT:LIST', '产品列表', 3, 21, '/api/products', 1, 1, 1),
(23, 'BUSINESS:PRODUCT:CREATE', '创建产品', 3, 21, '/api/products', 2, 1, 1),
(24, 'BUSINESS:PRODUCT:UPDATE', '更新产品', 3, 21, '/api/products/*', 3, 1, 1),
(25, 'BUSINESS:PRODUCT:DELETE', '删除产品', 3, 21, '/api/products/*', 4, 1, 1),
(26, 'BUSINESS:PRODUCT:PUBLISH', '发布产品', 3, 21, '/api/products/*/publish', 5, 1, 1),

-- 任务管理
(30, 'JOB', '任务管理', 1, 0, '/job', 3, 1, 1),
(31, 'JOB:TASK', '任务调度', 1, 30, '/job/task', 1, 1, 1),
(32, 'JOB:TASK:LIST', '任务列表', 3, 31, '/api/jobs', 1, 1, 1),
(33, 'JOB:TASK:CREATE', '创建任务', 3, 31, '/api/jobs', 2, 1, 1),
(34, 'JOB:TASK:UPDATE', '更新任务', 3, 31, '/api/jobs/*', 3, 1, 1),
(35, 'JOB:TASK:DELETE', '删除任务', 3, 31, '/api/jobs/*', 4, 1, 1),
(36, 'JOB:TASK:EXECUTE', '执行任务', 3, 31, '/api/jobs/*/run', 5, 1, 1);

-- 初始化用户角色关联
INSERT INTO `sys_user_role` (`user_id`, `role_id`, `create_by`) VALUES
(1, 1, 1), -- admin用户分配管理员角色
(2, 2, 1); -- user用户分配普通用户角色

-- 初始化角色权限关联
INSERT INTO `sys_role_permission` (`role_id`, `permission_id`, `create_by`) VALUES
-- 管理员角色拥有所有权限
(1, 1, 1), (1, 2, 1), (1, 3, 1), (1, 4, 1), (1, 5, 1), (1, 6, 1),
(1, 7, 1), (1, 8, 1), (1, 9, 1), (1, 10, 1), (1, 11, 1),
(1, 20, 1), (1, 21, 1), (1, 22, 1), (1, 23, 1), (1, 24, 1), (1, 25, 1), (1, 26, 1),
(1, 30, 1), (1, 31, 1), (1, 32, 1), (1, 33, 1), (1, 34, 1), (1, 35, 1), (1, 36, 1),

-- 普通用户角色拥有基本权限
(2, 20, 1), (2, 21, 1), (2, 22, 1), (2, 23, 1), (2, 24, 1),
(2, 30, 1), (2, 31, 1), (2, 32, 1),

-- 访客角色只有查看权限
(3, 20, 1), (3, 21, 1), (3, 22, 1),
(3, 30, 1), (3, 31, 1), (3, 32, 1);

-- =============================================
-- 业务服务初始化数据
-- =============================================
USE `microsystem_business`;

-- 初始化产品分类数据
INSERT INTO `biz_product_category` (`id`, `name`, `parent_id`, `level`, `sort_order`, `status`, `create_by`) VALUES
(1, '电子产品', 0, 1, 1, 1, 1),
(2, '手机', 1, 2, 1, 1, 1),
(3, '电脑', 1, 2, 2, 1, 1),
(4, '平板', 1, 2, 3, 1, 1),
(5, '服装鞋帽', 0, 1, 2, 1, 1),
(6, '男装', 5, 2, 1, 1, 1),
(7, '女装', 5, 2, 2, 1, 1),
(8, '童装', 5, 2, 3, 1, 1),
(9, '家居用品', 0, 1, 3, 1, 1),
(10, '厨具', 9, 2, 1, 1, 1);

-- 初始化产品数据
INSERT INTO `biz_product` (`id`, `name`, `description`, `price`, `status`, `category_id`, `category_name`, `stock`, `recommendation_score`, `create_by`) VALUES
(1, 'iPhone 15 Pro', '苹果最新旗舰手机，搭载A17 Pro芯片', 8999.00, 'PUBLISHED', 2, '手机', 100, 95.50, 1),
(2, 'MacBook Pro 16', '苹果专业级笔记本电脑，M3 Max芯片', 25999.00, 'PUBLISHED', 3, '电脑', 50, 92.80, 1),
(3, 'iPad Air', '轻薄便携的平板电脑，适合办公娱乐', 4399.00, 'PUBLISHED', 4, '平板', 80, 88.90, 1),
(4, '小米14 Ultra', '小米旗舰手机，徕卡影像系统', 5999.00, 'PUBLISHED', 2, '手机', 120, 89.70, 1),
(5, '华为MateBook X Pro', '华为高端商务笔记本', 8999.00, 'DRAFT', 3, '电脑', 30, 85.60, 1);

-- =============================================
-- 任务服务初始化数据
-- =============================================
USE `microsystem_job`;

-- 初始化任务执行日志数据（示例）
INSERT INTO `job_execution_log` (`job_id`, `instance_id`, `job_name`, `processor_info`, `status`, `result`, `start_time`, `end_time`, `duration`, `worker_address`) VALUES
(1001, 1001001, '数据同步任务', 'com.microsystem.job.processor.DataSyncJobProcessor', 'SUCCEED', '数据同步完成，共同步1000条记录', '2024-12-03 02:00:00', '2024-12-03 02:05:30', 330000, '*************:27777'),
(1002, 1002001, '报表生成任务', 'com.microsystem.job.processor.ReportGenerationJobProcessor', 'SUCCEED', '报表生成完成，文件路径: /reports/daily_report_20241203.xlsx', '2024-12-03 03:00:00', '2024-12-03 03:08:15', 495000, '*************:27777'),
(1003, 1003001, '数据清理任务', 'com.microsystem.job.processor.DataCleanupJobProcessor', 'SUCCEED', '数据清理完成，清理了500条过期记录，释放空间100MB', '2024-12-03 04:00:00', '2024-12-03 04:12:45', 765000, '*************:27777');
