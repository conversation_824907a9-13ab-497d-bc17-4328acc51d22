#!/bin/bash

# =============================================
# 微服务架构系统数据库部署脚本
# =============================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD:-"root123456"}
MYSQL_HOST=${MYSQL_HOST:-"localhost"}
MYSQL_PORT=${MYSQL_PORT:-"3306"}

REDIS_PASSWORD=${REDIS_PASSWORD:-""}
REDIS_HOST=${REDIS_HOST:-"localhost"}
REDIS_PORT=${REDIS_PORT:-"6379"}

# 检查MySQL连接
check_mysql_connection() {
    log_info "检查MySQL连接..."
    if mysql -h${MYSQL_HOST} -P${MYSQL_PORT} -uroot -p${MYSQL_ROOT_PASSWORD} -e "SELECT 1;" > /dev/null 2>&1; then
        log_info "MySQL连接成功"
        return 0
    else
        log_error "MySQL连接失败，请检查配置"
        return 1
    fi
}

# 检查Redis连接
check_redis_connection() {
    log_info "检查Redis连接..."
    if redis-cli -h ${REDIS_HOST} -p ${REDIS_PORT} ping > /dev/null 2>&1; then
        log_info "Redis连接成功"
        return 0
    else
        log_error "Redis连接失败，请检查配置"
        return 1
    fi
}

# 初始化MySQL数据库
init_mysql_databases() {
    log_info "初始化MySQL数据库..."
    
    # 执行数据库初始化脚本
    mysql -h${MYSQL_HOST} -P${MYSQL_PORT} -uroot -p${MYSQL_ROOT_PASSWORD} < ../database/init-databases.sql
    
    if [ $? -eq 0 ]; then
        log_info "数据库初始化成功"
    else
        log_error "数据库初始化失败"
        exit 1
    fi
    
    # 执行初始化数据脚本
    log_info "插入初始化数据..."
    mysql -h${MYSQL_HOST} -P${MYSQL_PORT} -uroot -p${MYSQL_ROOT_PASSWORD} < ../database/init-data.sql
    
    if [ $? -eq 0 ]; then
        log_info "初始化数据插入成功"
    else
        log_error "初始化数据插入失败"
        exit 1
    fi
}

# 创建Redis集群
setup_redis_cluster() {
    log_info "设置Redis集群..."
    
    # 检查Redis集群是否已存在
    if redis-cli -h ${REDIS_HOST} -p ${REDIS_PORT} cluster nodes > /dev/null 2>&1; then
        log_warn "Redis集群已存在，跳过创建"
        return 0
    fi
    
    # 创建Redis集群目录
    sudo mkdir -p /var/lib/redis/{7000,7001,7002,7003,7004,7005}
    sudo mkdir -p /var/log/redis
    sudo mkdir -p /var/run/redis
    
    # 复制配置文件
    for port in 7000 7001 7002 7003 7004 7005; do
        sudo cp ../config/redis-cluster.conf /etc/redis/redis-${port}.conf
        sudo sed -i "s/7000/${port}/g" /etc/redis/redis-${port}.conf
        sudo chown redis:redis /etc/redis/redis-${port}.conf
    done
    
    # 启动Redis实例
    for port in 7000 7001 7002 7003 7004 7005; do
        sudo systemctl start redis@${port}
        sudo systemctl enable redis@${port}
    done
    
    # 等待Redis实例启动
    sleep 5
    
    # 创建集群
    redis-cli --cluster create 127.0.0.1:7000 127.0.0.1:7001 127.0.0.1:7002 127.0.0.1:7003 127.0.0.1:7004 127.0.0.1:7005 --cluster-replicas 1 --cluster-yes
    
    if [ $? -eq 0 ]; then
        log_info "Redis集群创建成功"
    else
        log_error "Redis集群创建失败"
        exit 1
    fi
}

# 验证部署
verify_deployment() {
    log_info "验证部署结果..."
    
    # 验证MySQL数据库
    log_info "验证MySQL数据库..."
    mysql -h${MYSQL_HOST} -P${MYSQL_PORT} -uroot -p${MYSQL_ROOT_PASSWORD} -e "SHOW DATABASES;" | grep microsystem
    
    if [ $? -eq 0 ]; then
        log_info "MySQL数据库验证成功"
    else
        log_error "MySQL数据库验证失败"
    fi
    
    # 验证Redis集群
    log_info "验证Redis集群..."
    redis-cli -h ${REDIS_HOST} -p 7000 cluster info | grep cluster_state:ok
    
    if [ $? -eq 0 ]; then
        log_info "Redis集群验证成功"
    else
        log_error "Redis集群验证失败"
    fi
}

# 主函数
main() {
    log_info "开始部署微服务架构系统数据库..."
    
    # 检查连接
    check_mysql_connection || exit 1
    check_redis_connection || exit 1
    
    # 初始化数据库
    init_mysql_databases
    
    # 设置Redis集群
    setup_redis_cluster
    
    # 验证部署
    verify_deployment
    
    log_info "数据库部署完成！"
}

# 显示帮助信息
show_help() {
    echo "微服务架构系统数据库部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示帮助信息"
    echo "  --mysql-host HOST       MySQL主机地址 (默认: localhost)"
    echo "  --mysql-port PORT       MySQL端口 (默认: 3306)"
    echo "  --mysql-password PASS   MySQL root密码 (默认: root123456)"
    echo "  --redis-host HOST       Redis主机地址 (默认: localhost)"
    echo "  --redis-port PORT       Redis端口 (默认: 6379)"
    echo ""
    echo "环境变量:"
    echo "  MYSQL_ROOT_PASSWORD     MySQL root密码"
    echo "  MYSQL_HOST              MySQL主机地址"
    echo "  MYSQL_PORT              MySQL端口"
    echo "  REDIS_HOST              Redis主机地址"
    echo "  REDIS_PORT              Redis端口"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --mysql-host)
            MYSQL_HOST="$2"
            shift 2
            ;;
        --mysql-port)
            MYSQL_PORT="$2"
            shift 2
            ;;
        --mysql-password)
            MYSQL_ROOT_PASSWORD="$2"
            shift 2
            ;;
        --redis-host)
            REDIS_HOST="$2"
            shift 2
            ;;
        --redis-port)
            REDIS_PORT="$2"
            shift 2
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行主函数
main
