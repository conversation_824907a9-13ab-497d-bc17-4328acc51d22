#!/bin/bash

# =============================================
# 微服务架构系统中间件部署脚本
# =============================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 配置变量
NACOS_VERSION=${NACOS_VERSION:-"2.3.0"}
SENTINEL_VERSION=${SENTINEL_VERSION:-"1.8.6"}
PULSAR_VERSION=${PULSAR_VERSION:-"3.1.1"}
POWERJOB_VERSION=${POWERJOB_VERSION:-"4.3.6"}

INSTALL_DIR=${INSTALL_DIR:-"/opt/microsystem"}
DATA_DIR=${DATA_DIR:-"/data/microsystem"}

# 创建目录
create_directories() {
    log_info "创建安装目录..."
    sudo mkdir -p ${INSTALL_DIR}/{nacos,sentinel,pulsar,powerjob}
    sudo mkdir -p ${DATA_DIR}/{nacos,sentinel,pulsar,powerjob}
    sudo mkdir -p /var/log/microsystem
}

# 安装Nacos
install_nacos() {
    log_info "安装Nacos ${NACOS_VERSION}..."
    
    cd ${INSTALL_DIR}/nacos
    
    # 下载Nacos
    if [ ! -f "nacos-server-${NACOS_VERSION}.tar.gz" ]; then
        wget https://github.com/alibaba/nacos/releases/download/${NACOS_VERSION}/nacos-server-${NACOS_VERSION}.tar.gz
    fi
    
    # 解压
    tar -xzf nacos-server-${NACOS_VERSION}.tar.gz
    
    # 配置Nacos
    cat > nacos/conf/application.properties << EOF
# Nacos配置
server.contextPath=/nacos
server.servlet.contextPath=/nacos
server.port=8848

# 数据库配置
spring.datasource.platform=mysql
db.num=1
db.url.0=******************************************************************************************************************************************************************
db.user.0=nacos
db.password.0=nacos

# 集群配置
nacos.cmdb.dumpTaskInterval=3600
nacos.cmdb.eventTaskInterval=10
nacos.cmdb.labelTaskInterval=300
nacos.cmdb.loadDataAtStart=false

# 日志配置
nacos.logs.path=${DATA_DIR}/nacos/logs
EOF
    
    # 创建启动脚本
    cat > start-nacos.sh << 'EOF'
#!/bin/bash
cd $(dirname $0)/nacos/bin
./startup.sh -m standalone
EOF
    chmod +x start-nacos.sh
    
    log_info "Nacos安装完成"
}

# 安装Sentinel
install_sentinel() {
    log_info "安装Sentinel ${SENTINEL_VERSION}..."
    
    cd ${INSTALL_DIR}/sentinel
    
    # 下载Sentinel Dashboard
    if [ ! -f "sentinel-dashboard-${SENTINEL_VERSION}.jar" ]; then
        wget https://github.com/alibaba/Sentinel/releases/download/${SENTINEL_VERSION}/sentinel-dashboard-${SENTINEL_VERSION}.jar
    fi
    
    # 创建配置文件
    cat > application.properties << EOF
# Sentinel Dashboard配置
server.port=8080
server.servlet.session.timeout=7200

# 登录配置
sentinel.dashboard.auth.username=sentinel
sentinel.dashboard.auth.password=sentinel

# 数据持久化配置
sentinel.dashboard.rule.nacos.server-addr=localhost:8848
sentinel.dashboard.rule.nacos.username=nacos
sentinel.dashboard.rule.nacos.password=nacos
sentinel.dashboard.rule.nacos.namespace=dev
EOF
    
    # 创建启动脚本
    cat > start-sentinel.sh << EOF
#!/bin/bash
java -Dserver.port=8080 \\
     -Dcsp.sentinel.dashboard.server=localhost:8080 \\
     -Dproject.name=sentinel-dashboard \\
     -Dsentinel.dashboard.auth.username=sentinel \\
     -Dsentinel.dashboard.auth.password=sentinel \\
     -jar sentinel-dashboard-${SENTINEL_VERSION}.jar
EOF
    chmod +x start-sentinel.sh
    
    log_info "Sentinel安装完成"
}

# 安装Pulsar
install_pulsar() {
    log_info "安装Pulsar ${PULSAR_VERSION}..."
    
    cd ${INSTALL_DIR}/pulsar
    
    # 下载Pulsar
    if [ ! -f "apache-pulsar-${PULSAR_VERSION}-bin.tar.gz" ]; then
        wget https://archive.apache.org/dist/pulsar/pulsar-${PULSAR_VERSION}/apache-pulsar-${PULSAR_VERSION}-bin.tar.gz
    fi
    
    # 解压
    tar -xzf apache-pulsar-${PULSAR_VERSION}-bin.tar.gz
    ln -sf apache-pulsar-${PULSAR_VERSION} current
    
    # 配置Pulsar
    cat > current/conf/standalone.conf << EOF
# Pulsar Standalone配置
advertisedAddress=localhost
bindAddress=0.0.0.0

# Web服务配置
webServicePort=8080
webServicePortTls=8443

# Broker配置
brokerServicePort=6650
brokerServicePortTls=6651

# 数据存储配置
managedLedgerDefaultEnsembleSize=1
managedLedgerDefaultWriteQuorum=1
managedLedgerDefaultAckQuorum=1

# 元数据存储
metadataStoreUrl=memory:local
configurationMetadataStoreUrl=memory:local

# 日志配置
PULSAR_LOG_CONF=${DATA_DIR}/pulsar/conf/log4j2.yaml
EOF
    
    # 创建启动脚本
    cat > start-pulsar.sh << 'EOF'
#!/bin/bash
cd $(dirname $0)/current
bin/pulsar standalone
EOF
    chmod +x start-pulsar.sh
    
    log_info "Pulsar安装完成"
}

# 安装PowerJob
install_powerjob() {
    log_info "安装PowerJob ${POWERJOB_VERSION}..."
    
    cd ${INSTALL_DIR}/powerjob
    
    # 下载PowerJob Server
    if [ ! -f "powerjob-server-${POWERJOB_VERSION}.jar" ]; then
        wget https://github.com/PowerJob/PowerJob/releases/download/v${POWERJOB_VERSION}/powerjob-server-${POWERJOB_VERSION}.jar
    fi
    
    # 创建配置文件
    cat > application.properties << EOF
# PowerJob Server配置
server.port=7700

# 数据库配置
spring.datasource.core.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.core.jdbc-url=*********************************************************************************************************
spring.datasource.core.username=powerjob
spring.datasource.core.password=powerjob

# 日志配置
logging.config=classpath:logback-spring.xml
oms.log.path=${DATA_DIR}/powerjob/logs

# 集群配置
oms.akka.port=25520
oms.http.port=7700
EOF
    
    # 创建启动脚本
    cat > start-powerjob.sh << EOF
#!/bin/bash
java -server \\
     -Xms1024m -Xmx1024m \\
     -XX:MetaspaceSize=128m \\
     -Dfile.encoding=UTF-8 \\
     -Duser.timezone=Asia/Shanghai \\
     -jar powerjob-server-${POWERJOB_VERSION}.jar \\
     --spring.config.location=application.properties
EOF
    chmod +x start-powerjob.sh
    
    log_info "PowerJob安装完成"
}

# 配置系统服务
setup_systemd_services() {
    log_info "配置系统服务..."
    
    # Nacos服务
    sudo tee /etc/systemd/system/nacos.service > /dev/null << EOF
[Unit]
Description=Nacos Server
After=network.target

[Service]
Type=forking
User=root
ExecStart=${INSTALL_DIR}/nacos/start-nacos.sh
ExecStop=/bin/kill -15 \$MAINPID
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

    # Sentinel服务
    sudo tee /etc/systemd/system/sentinel.service > /dev/null << EOF
[Unit]
Description=Sentinel Dashboard
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=${INSTALL_DIR}/sentinel
ExecStart=${INSTALL_DIR}/sentinel/start-sentinel.sh
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

    # Pulsar服务
    sudo tee /etc/systemd/system/pulsar.service > /dev/null << EOF
[Unit]
Description=Apache Pulsar
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=${INSTALL_DIR}/pulsar
ExecStart=${INSTALL_DIR}/pulsar/start-pulsar.sh
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

    # PowerJob服务
    sudo tee /etc/systemd/system/powerjob.service > /dev/null << EOF
[Unit]
Description=PowerJob Server
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=${INSTALL_DIR}/powerjob
ExecStart=${INSTALL_DIR}/powerjob/start-powerjob.sh
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

    # 重新加载systemd
    sudo systemctl daemon-reload
    
    log_info "系统服务配置完成"
}

# 启动服务
start_services() {
    log_info "启动中间件服务..."
    
    # 启动Nacos
    sudo systemctl start nacos
    sudo systemctl enable nacos
    
    # 等待Nacos启动
    sleep 10
    
    # 启动Sentinel
    sudo systemctl start sentinel
    sudo systemctl enable sentinel
    
    # 启动Pulsar
    sudo systemctl start pulsar
    sudo systemctl enable pulsar
    
    # 启动PowerJob
    sudo systemctl start powerjob
    sudo systemctl enable powerjob
    
    log_info "所有服务启动完成"
}

# 验证安装
verify_installation() {
    log_info "验证中间件安装..."
    
    # 检查Nacos
    if curl -f http://localhost:8848/nacos > /dev/null 2>&1; then
        log_info "✓ Nacos运行正常"
    else
        log_error "✗ Nacos启动失败"
    fi
    
    # 检查Sentinel
    if curl -f http://localhost:8080 > /dev/null 2>&1; then
        log_info "✓ Sentinel运行正常"
    else
        log_error "✗ Sentinel启动失败"
    fi
    
    # 检查Pulsar
    if curl -f http://localhost:8080/admin/v2/clusters > /dev/null 2>&1; then
        log_info "✓ Pulsar运行正常"
    else
        log_error "✗ Pulsar启动失败"
    fi
    
    # 检查PowerJob
    if curl -f http://localhost:7700 > /dev/null 2>&1; then
        log_info "✓ PowerJob运行正常"
    else
        log_error "✗ PowerJob启动失败"
    fi
}

# 主函数
main() {
    log_info "开始部署微服务架构系统中间件..."
    
    create_directories
    install_nacos
    install_sentinel
    install_pulsar
    install_powerjob
    setup_systemd_services
    start_services
    
    sleep 30  # 等待服务完全启动
    
    verify_installation
    
    log_info "中间件部署完成！"
    log_info "访问地址："
    log_info "  Nacos: http://localhost:8848/nacos (nacos/nacos)"
    log_info "  Sentinel: http://localhost:8080 (sentinel/sentinel)"
    log_info "  Pulsar: http://localhost:8080"
    log_info "  PowerJob: http://localhost:7700 (admin/123456)"
}

# 显示帮助信息
show_help() {
    echo "微服务架构系统中间件部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示帮助信息"
    echo "  --install-dir DIR       安装目录 (默认: /opt/microsystem)"
    echo "  --data-dir DIR          数据目录 (默认: /data/microsystem)"
    echo "  --nacos-version VER     Nacos版本 (默认: 2.3.0)"
    echo "  --sentinel-version VER  Sentinel版本 (默认: 1.8.6)"
    echo "  --pulsar-version VER    Pulsar版本 (默认: 3.1.1)"
    echo "  --powerjob-version VER  PowerJob版本 (默认: 4.3.6)"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --install-dir)
            INSTALL_DIR="$2"
            shift 2
            ;;
        --data-dir)
            DATA_DIR="$2"
            shift 2
            ;;
        --nacos-version)
            NACOS_VERSION="$2"
            shift 2
            ;;
        --sentinel-version)
            SENTINEL_VERSION="$2"
            shift 2
            ;;
        --pulsar-version)
            PULSAR_VERSION="$2"
            shift 2
            ;;
        --powerjob-version)
            POWERJOB_VERSION="$2"
            shift 2
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行主函数
main
