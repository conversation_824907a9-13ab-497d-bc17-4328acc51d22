# MicroSystem - 生产级微服务架构系统

[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Java](https://img.shields.io/badge/Java-17-orange.svg)](https://openjdk.java.net/projects/jdk/17/)
[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.2-green.svg)](https://spring.io/projects/spring-boot)
[![Spring Cloud](https://img.shields.io/badge/Spring%20Cloud-2023.0-blue.svg)](https://spring.io/projects/spring-cloud)

## 📋 项目简介

MicroSystem是一个基于Spring Cloud Alibaba技术栈构建的生产级微服务架构系统，采用领域驱动设计（DDD）模式，集成了现代微服务开发所需的各种组件和最佳实践。

### 🎯 核心特性

- **微服务架构**: 基于Spring Cloud Alibaba构建的分布式微服务系统
- **领域驱动设计**: 采用DDD架构模式，清晰的业务边界和职责分离
- **服务注册发现**: 使用Nacos作为注册中心和配置中心
- **API网关**: Spring Cloud Gateway提供统一的API入口
- **熔断限流**: Sentinel实现服务熔断、限流和降级
- **分布式事务**: 支持分布式事务处理
- **消息队列**: Apache Pulsar实现异步消息处理
- **任务调度**: PowerJob提供分布式任务调度能力
- **数据分片**: ShardingJDBC实现数据库分库分表
- **缓存支持**: Redis集群提供高性能缓存
- **容器化部署**: Docker和Docker Compose支持
- **监控告警**: 完整的监控和告警体系

## 🏗️ 系统架构

### 技术栈

| 组件 | 版本 | 说明 |
|------|------|------|
| Java | 17 | 编程语言 |
| Spring Boot | 3.2.x | 应用框架 |
| Spring Cloud | 2023.0.x | 微服务框架 |
| Spring Cloud Alibaba | 2023.0.x | 阿里巴巴微服务套件 |
| Nacos | 2.3.0 | 注册中心/配置中心 |
| Sentinel | 1.8.6 | 熔断限流 |
| Spring Cloud Gateway | - | API网关 |
| MySQL | 8.0 | 关系型数据库 |
| Redis | 7.x | 缓存数据库 |
| Apache Pulsar | 3.1.1 | 消息队列 |
| PowerJob | 4.3.6 | 分布式任务调度 |
| ShardingJDBC | 5.x | 数据分片 |
| MyBatis-Plus | 3.5.x | ORM框架 |
| Druid | 1.2.x | 数据库连接池 |

### 服务架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Client    │    │  Mobile Client  │    │  Third Party    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │  Gateway Service │
                    │   (Port: 8080)   │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Auth Service   │    │ Business Service │    │  Job Service    │
│  (Port: 8081)   │    │  (Port: 8082)    │    │  (Port: 8083)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
              ┌─────────────────────────────────────┐
              │           Infrastructure            │
              │  ┌─────────┐ ┌─────────┐ ┌─────────┐│
              │  │  Nacos  │ │  Redis  │ │  MySQL  ││
              │  └─────────┘ └─────────┘ └─────────┘│
              │  ┌─────────┐ ┌─────────┐ ┌─────────┐│
              │  │Sentinel │ │ Pulsar  │ │PowerJob ││
              │  └─────────┘ └─────────┘ └─────────┘│
              └─────────────────────────────────────┘
```

## 🚀 快速开始

### 环境要求

- Java 17+
- Maven 3.8+
- Docker 20.10+
- Docker Compose 2.0+

### 本地开发环境搭建

1. **克隆项目**
```bash
git clone https://github.com/your-org/microsystem.git
cd microsystem
```

2. **启动基础设施**
```bash
# 启动MySQL、Redis、Nacos等基础设施
cd docker
docker-compose up -d mysql redis nacos
```

3. **构建项目**
```bash
# 返回项目根目录
cd ..
mvn clean package -DskipTests
```

4. **启动微服务**
```bash
# 启动认证服务
cd auth-service
mvn spring-boot:run

# 启动业务服务
cd ../business-service
mvn spring-boot:run

# 启动任务服务
cd ../job-service
mvn spring-boot:run

# 启动网关服务
cd ../gateway-service
mvn spring-boot:run
```

### Docker容器化部署

1. **一键构建和部署**
```bash
# 构建所有服务镜像
./scripts/docker/build-images.sh

# 一键部署整个系统
./scripts/docker/deploy.sh
```

2. **验证部署**
```bash
# 检查服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f gateway-service
```

## 📚 API文档

### 服务端口

| 服务 | 端口 | 描述 |
|------|------|------|
| Gateway Service | 8080 | API网关 |
| Auth Service | 8081 | 认证服务 |
| Business Service | 8082 | 业务服务 |
| Job Service | 8083 | 任务服务 |

### API文档地址

- **认证服务**: http://localhost:8081/swagger-ui.html
- **业务服务**: http://localhost:8082/swagger-ui.html
- **任务服务**: http://localhost:8083/swagger-ui.html

### 管理控制台

- **Nacos**: http://localhost:8848/nacos (nacos/nacos)
- **Sentinel**: http://localhost:8858 (sentinel/sentinel)
- **Pulsar**: http://localhost:8090
- **PowerJob**: http://localhost:7700 (admin/123456)

## 🧪 测试

### 运行单元测试
```bash
mvn test
```

### 运行集成测试
```bash
mvn verify
```

### 测试覆盖率报告
```bash
mvn jacoco:report
```

## 📖 详细文档

- [部署指南](docs/deployment.md)
- [API接口文档](docs/api.md)
- [开发指南](docs/development.md)
- [架构设计](docs/architecture.md)
- [故障排查](docs/troubleshooting.md)

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 👥 团队

- **MicroSystem Team** - *Initial work* - [<EMAIL>](mailto:<EMAIL>)

## 🙏 致谢

感谢以下开源项目的支持：
- [Spring Cloud Alibaba](https://github.com/alibaba/spring-cloud-alibaba)
- [Apache Pulsar](https://pulsar.apache.org/)
- [PowerJob](https://github.com/PowerJob/PowerJob)
- [Nacos](https://nacos.io/)
- [Sentinel](https://sentinelguard.io/)

---

⭐ 如果这个项目对你有帮助，请给我们一个星标！
