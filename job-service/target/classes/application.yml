server:
  port: 8083
  servlet:
    context-path: /

spring:
  application:
    name: job-service
  profiles:
    active: dev
  
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************************************************
    username: root
    password: 123456
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall,slf4j
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password: 
      database: 2
      timeout: 10000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0

  # Cloud配置
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: dev
        group: DEFAULT_GROUP
      config:
        server-addr: localhost:8848
        namespace: dev
        group: DEFAULT_GROUP
        file-extension: yml
        shared-configs:
          - data-id: common-config.yml
            group: DEFAULT_GROUP
            refresh: true
    
    # Sentinel配置
    sentinel:
      transport:
        dashboard: localhost:8080
        port: 8719
      datasource:
        ds1:
          nacos:
            server-addr: localhost:8848
            dataId: job-service-sentinel
            groupId: DEFAULT_GROUP
            data-type: json
            rule-type: flow

# PowerJob Worker配置
powerjob:
  worker:
    # 应用名称，用于分组隔离
    app-name: microsystem-job-worker
    # 调度服务器地址
    server-address: 127.0.0.1:7700
    # 本机地址，可选配置，如果不配置会自动获取
    # worker-address: 127.0.0.1:27777
    # 端口号，可选配置，默认27777
    port: 27777
    # 协议，可选配置，默认akka
    protocol: akka
    # 存储策略，可选配置，默认disk
    store-strategy: disk
    # 最大结果长度，可选配置，默认8192
    max-result-length: 8192
    # 最大追加长度，可选配置，默认8192
    max-appended-wf-context-length: 8192
    # 健康检查间隔，可选配置，默认10s
    health-report-interval: 10000
    # 允许延迟上报时间，可选配置，默认3s
    max-heavy-weight-task-num: 1024
    # 最大轻量级任务数量，可选配置，默认1024
    max-light-weight-task-num: 1024

# MyBatis Plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.microsystem.job.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  global-config:
    db-config:
      id-type: ASSIGN_ID
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# Pulsar配置
pulsar:
  service-url: pulsar://localhost:6650
  admin-url: http://localhost:8080
  namespace: microsystem
  tenant: public

# 日志配置
logging:
  level:
    com.microsystem: DEBUG
    tech.powerjob: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"

# Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always

# Swagger配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  info:
    title: 任务调度服务API
    description: 微服务架构任务调度服务接口文档
    version: 1.0.0
    contact:
      name: MicroSystem Team
      email: <EMAIL>

# 任务配置
job:
  # 任务执行超时时间(分钟)
  execution-timeout: 30
  # 任务重试次数
  retry-times: 3
  # 任务结果保留天数
  result-retention-days: 30
