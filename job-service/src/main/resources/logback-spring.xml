<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds">
    
    <!-- 引入Spring Boot默认配置 -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    
    <!-- 定义日志文件路径 -->
    <springProfile name="!docker">
        <property name="LOG_PATH" value="./logs/job-service"/>
    </springProfile>
    <springProfile name="docker">
        <property name="LOG_PATH" value="/app/logs"/>
    </springProfile>
    
    <!-- 定义日志格式 -->
    <property name="LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-}] [%X{jobId:-}] [%X{instanceId:-}] %logger{50} - %msg%n"/>
    
    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
    </appender>
    
    <!-- 异步控制台输出 -->
    <appender name="ASYNC_CONSOLE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="CONSOLE"/>
        <queueSize>1024</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <maxFlushTime>2000</maxFlushTime>
        <neverBlock>true</neverBlock>
    </appender>
    
    <!-- 应用日志文件 -->
    <appender name="APP_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/job-service.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/job-service.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    
    <!-- 异步应用日志 -->
    <appender name="ASYNC_APP_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="APP_FILE"/>
        <queueSize>2048</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <maxFlushTime>2000</maxFlushTime>
        <neverBlock>true</neverBlock>
    </appender>
    
    <!-- 错误日志文件 -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/job-service-error.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/job-service-error.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>60</maxHistory>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    
    <!-- 异步错误日志 -->
    <appender name="ASYNC_ERROR_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="ERROR_FILE"/>
        <queueSize>1024</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <maxFlushTime>2000</maxFlushTime>
        <neverBlock>true</neverBlock>
    </appender>
    
    <!-- 任务执行日志 -->
    <appender name="JOB_EXECUTION_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/job-service-execution.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/job-service-execution.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>200MB</maxFileSize>
            <maxHistory>90</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- 异步任务执行日志 -->
    <appender name="ASYNC_JOB_EXECUTION_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="JOB_EXECUTION_FILE"/>
        <queueSize>4096</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <maxFlushTime>1000</maxFlushTime>
        <neverBlock>true</neverBlock>
    </appender>
    
    <!-- 任务调度日志 -->
    <appender name="JOB_SCHEDULE_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/job-service-schedule.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/job-service-schedule.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- 异步任务调度日志 -->
    <appender name="ASYNC_JOB_SCHEDULE_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="JOB_SCHEDULE_FILE"/>
        <queueSize>2048</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <maxFlushTime>1000</maxFlushTime>
        <neverBlock>true</neverBlock>
    </appender>
    
    <!-- PowerJob Worker日志 -->
    <appender name="POWERJOB_WORKER_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/job-service-powerjob.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/job-service-powerjob.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- 异步PowerJob Worker日志 -->
    <appender name="ASYNC_POWERJOB_WORKER_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="POWERJOB_WORKER_FILE"/>
        <queueSize>2048</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <maxFlushTime>1000</maxFlushTime>
        <neverBlock>true</neverBlock>
    </appender>
    
    <!-- 业务关键操作日志 -->
    <appender name="BUSINESS_CRITICAL_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/job-service-critical.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/job-service-critical.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>90</maxHistory>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- 异步业务关键操作日志 -->
    <appender name="ASYNC_BUSINESS_CRITICAL_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="BUSINESS_CRITICAL_FILE"/>
        <queueSize>2048</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <maxFlushTime>2000</maxFlushTime>
        <neverBlock>true</neverBlock>
    </appender>
    
    <!-- 特定Logger配置 -->
    <logger name="BUSINESS_CRITICAL" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_BUSINESS_CRITICAL_FILE"/>
    </logger>
    
    <logger name="JOB_EXECUTION" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_JOB_EXECUTION_FILE"/>
    </logger>
    
    <logger name="JOB_SCHEDULE" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_JOB_SCHEDULE_FILE"/>
    </logger>
    
    <!-- PowerJob相关日志 -->
    <logger name="tech.powerjob" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_POWERJOB_WORKER_FILE"/>
    </logger>
    
    <!-- 应用包日志级别 -->
    <logger name="com.microsystem.job" level="INFO"/>
    <logger name="com.microsystem.common" level="INFO"/>
    
    <!-- 第三方库日志级别 -->
    <logger name="org.springframework" level="WARN"/>
    <logger name="org.apache" level="WARN"/>
    <logger name="com.alibaba" level="WARN"/>
    <logger name="io.lettuce" level="WARN"/>
    <logger name="com.zaxxer.hikari" level="WARN"/>
    
    <!-- 根Logger配置 -->
    <springProfile name="dev,test">
        <root level="DEBUG">
            <appender-ref ref="ASYNC_CONSOLE"/>
            <appender-ref ref="ASYNC_APP_FILE"/>
            <appender-ref ref="ASYNC_ERROR_FILE"/>
        </root>
    </springProfile>
    
    <springProfile name="prod,docker">
        <root level="INFO">
            <appender-ref ref="ASYNC_APP_FILE"/>
            <appender-ref ref="ASYNC_ERROR_FILE"/>
        </root>
    </springProfile>
    
</configuration>
