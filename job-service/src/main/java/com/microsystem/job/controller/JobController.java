package com.microsystem.job.controller;

import com.microsystem.common.result.Result;
import com.microsystem.job.dto.request.CreateJobRequest;
import com.microsystem.job.dto.request.UpdateJobRequest;
import com.microsystem.job.dto.response.JobInfoResponse;
import com.microsystem.job.dto.response.JobInstanceResponse;
import com.microsystem.job.service.JobManagementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 任务管理控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/jobs")
@RequiredArgsConstructor
@Tag(name = "任务管理", description = "分布式任务调度管理接口")
public class JobController {

    private final JobManagementService jobManagementService;

    /**
     * 创建任务
     */
    @PostMapping
    @Operation(summary = "创建任务", description = "创建新的定时任务")
    public Result<JobInfoResponse> createJob(@Valid @RequestBody CreateJobRequest request) {
        log.info("创建任务请求: {}", request.getJobName());
        JobInfoResponse response = jobManagementService.createJob(request);
        return Result.success("任务创建成功", response);
    }

    /**
     * 更新任务
     */
    @PutMapping("/{jobId}")
    @Operation(summary = "更新任务", description = "更新指定任务信息")
    public Result<JobInfoResponse> updateJob(
            @Parameter(description = "任务ID") @PathVariable Long jobId,
            @Valid @RequestBody UpdateJobRequest request) {
        log.info("更新任务请求: {}", jobId);
        JobInfoResponse response = jobManagementService.updateJob(jobId, request);
        return Result.success("任务更新成功", response);
    }

    /**
     * 获取任务详情
     */
    @GetMapping("/{jobId}")
    @Operation(summary = "获取任务详情", description = "根据ID获取任务详细信息")
    public Result<JobInfoResponse> getJobInfo(
            @Parameter(description = "任务ID") @PathVariable Long jobId) {
        log.info("获取任务详情: {}", jobId);
        JobInfoResponse response = jobManagementService.getJobInfo(jobId);
        return Result.success(response);
    }

    /**
     * 获取任务列表
     */
    @GetMapping
    @Operation(summary = "获取任务列表", description = "获取所有任务列表")
    public Result<List<JobInfoResponse>> getJobList() {
        log.info("获取任务列表");
        List<JobInfoResponse> response = jobManagementService.getJobList();
        return Result.success(response);
    }

    /**
     * 启用任务
     */
    @PostMapping("/{jobId}/enable")
    @Operation(summary = "启用任务", description = "启用指定任务")
    public Result<Void> enableJob(
            @Parameter(description = "任务ID") @PathVariable Long jobId) {
        log.info("启用任务: {}", jobId);
        jobManagementService.enableJob(jobId);
        return Result.success("任务启用成功");
    }

    /**
     * 禁用任务
     */
    @PostMapping("/{jobId}/disable")
    @Operation(summary = "禁用任务", description = "禁用指定任务")
    public Result<Void> disableJob(
            @Parameter(description = "任务ID") @PathVariable Long jobId) {
        log.info("禁用任务: {}", jobId);
        jobManagementService.disableJob(jobId);
        return Result.success("任务禁用成功");
    }

    /**
     * 删除任务
     */
    @DeleteMapping("/{jobId}")
    @Operation(summary = "删除任务", description = "删除指定任务")
    public Result<Void> deleteJob(
            @Parameter(description = "任务ID") @PathVariable Long jobId) {
        log.info("删除任务: {}", jobId);
        jobManagementService.deleteJob(jobId);
        return Result.success("任务删除成功");
    }

    /**
     * 立即执行任务
     */
    @PostMapping("/{jobId}/run")
    @Operation(summary = "立即执行任务", description = "立即触发任务执行")
    public Result<Long> runJobNow(
            @Parameter(description = "任务ID") @PathVariable Long jobId,
            @Parameter(description = "实例参数") @RequestParam(required = false) String instanceParams) {
        log.info("立即执行任务: {}", jobId);
        Long instanceId = jobManagementService.runJobNow(jobId, instanceParams);
        return Result.success("任务执行成功", instanceId);
    }

    /**
     * 停止任务实例
     */
    @PostMapping("/instances/{instanceId}/stop")
    @Operation(summary = "停止任务实例", description = "停止正在执行的任务实例")
    public Result<Void> stopInstance(
            @Parameter(description = "实例ID") @PathVariable Long instanceId) {
        log.info("停止任务实例: {}", instanceId);
        jobManagementService.stopInstance(instanceId);
        return Result.success("任务实例停止成功");
    }

    /**
     * 获取任务实例列表
     */
    @GetMapping("/{jobId}/instances")
    @Operation(summary = "获取任务实例列表", description = "获取指定任务的实例列表")
    public Result<List<JobInstanceResponse>> getJobInstances(
            @Parameter(description = "任务ID") @PathVariable Long jobId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size) {
        log.info("获取任务实例列表: jobId={}, page={}, size={}", jobId, page, size);
        List<JobInstanceResponse> response = jobManagementService.getJobInstances(jobId, page, size);
        return Result.success(response);
    }

    /**
     * 获取任务实例详情
     */
    @GetMapping("/instances/{instanceId}")
    @Operation(summary = "获取任务实例详情", description = "根据实例ID获取任务实例详细信息")
    public Result<JobInstanceResponse> getJobInstanceInfo(
            @Parameter(description = "实例ID") @PathVariable Long instanceId) {
        log.info("获取任务实例详情: {}", instanceId);
        JobInstanceResponse response = jobManagementService.getJobInstanceInfo(instanceId);
        return Result.success(response);
    }
}
