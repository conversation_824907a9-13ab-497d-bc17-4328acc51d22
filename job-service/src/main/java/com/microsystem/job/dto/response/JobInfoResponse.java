package com.microsystem.job.dto.response;

import lombok.Data;

/**
 * 任务信息响应DTO
 * 
 * <AUTHOR>
 */
@Data
public class JobInfoResponse {

    /**
     * 任务ID
     */
    private Long jobId;

    /**
     * 任务名称
     */
    private String jobName;

    /**
     * 任务描述
     */
    private String jobDescription;

    /**
     * 处理器信息
     */
    private String processorInfo;

    /**
     * Cron表达式
     */
    private String cronExpression;

    /**
     * 任务参数
     */
    private String jobParams;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 下次触发时间
     */
    private Long nextTriggerTime;

    /**
     * 最大实例数
     */
    private Integer maxInstanceNum;

    /**
     * 并发度
     */
    private Integer concurrency;

    /**
     * 实例超时时间(ms)
     */
    private Long instanceTimeLimit;

    /**
     * 实例重试次数
     */
    private Integer instanceRetryNum;

    /**
     * 报警用户ID列表
     */
    private String notifyUserIds;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;
}
