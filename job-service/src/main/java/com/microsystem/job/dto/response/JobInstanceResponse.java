package com.microsystem.job.dto.response;

import lombok.Data;

/**
 * 任务实例响应DTO
 * 
 * <AUTHOR>
 */
@Data
public class JobInstanceResponse {

    /**
     * 实例ID
     */
    private Long instanceId;

    /**
     * 任务ID
     */
    private Long jobId;

    /**
     * 实例参数
     */
    private String instanceParams;

    /**
     * 实例状态
     */
    private String status;

    /**
     * 执行结果
     */
    private String result;

    /**
     * 预期触发时间
     */
    private Long expectedTriggerTime;

    /**
     * 实际触发时间
     */
    private Long actualTriggerTime;

    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 结束时间
     */
    private Long finishTime;

    /**
     * 运行次数
     */
    private Integer runningTimes;

    /**
     * 任务追踪器
     */
    private String taskTrackerAddress;

    /**
     * 执行地址
     */
    private String workerAddress;
}
