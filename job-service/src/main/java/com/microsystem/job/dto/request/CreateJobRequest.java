package com.microsystem.job.dto.request;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 创建任务请求DTO
 * 
 * <AUTHOR>
 */
@Data
public class CreateJobRequest {

    /**
     * 任务名称
     */
    @NotBlank(message = "任务名称不能为空")
    private String jobName;

    /**
     * 任务描述
     */
    private String jobDescription;

    /**
     * 处理器信息
     */
    @NotBlank(message = "处理器信息不能为空")
    private String processorInfo;

    /**
     * Cron表达式
     */
    private String cronExpression;

    /**
     * 任务参数
     */
    private String jobParams;

    /**
     * 最大实例数
     */
    private Integer maxInstanceNum = 1;

    /**
     * 并发度
     */
    private Integer concurrency = 5;

    /**
     * 实例超时时间(ms)
     */
    private Long instanceTimeLimit = 0L;

    /**
     * 实例重试次数
     */
    private Integer instanceRetryNum = 3;

    /**
     * 任务整体超时时间(ms)
     */
    private Long taskRetryNum = 3L;

    /**
     * 最低CPU核心数量
     */
    private Double minCpuCores = 0.0;

    /**
     * 最低内存空间(GB)
     */
    private Double minMemorySpace = 0.0;

    /**
     * 最低磁盘空间(GB)
     */
    private Double minDiskSpace = 0.0;

    /**
     * 指定机器执行
     */
    private String designatedWorkers;

    /**
     * 最大机器数量
     */
    private Integer maxWorkerCount = 0;

    /**
     * 报警用户ID列表
     */
    private String notifyUserIds;

    /**
     * 是否启用
     */
    private Boolean enable = true;
}
