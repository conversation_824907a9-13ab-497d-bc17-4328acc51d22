package com.microsystem.job.dto.request;

import lombok.Data;

/**
 * 更新任务请求DTO
 * 
 * <AUTHOR>
 */
@Data
public class UpdateJobRequest {

    /**
     * 任务名称
     */
    private String jobName;

    /**
     * 任务描述
     */
    private String jobDescription;

    /**
     * Cron表达式
     */
    private String cronExpression;

    /**
     * 任务参数
     */
    private String jobParams;

    /**
     * 最大实例数
     */
    private Integer maxInstanceNum;

    /**
     * 并发度
     */
    private Integer concurrency;

    /**
     * 实例超时时间(ms)
     */
    private Long instanceTimeLimit;

    /**
     * 实例重试次数
     */
    private Integer instanceRetryNum;

    /**
     * 报警用户ID列表
     */
    private String notifyUserIds;
}
