package com.microsystem.job.processor;

import lombok.extern.slf4j.Slf4j;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

/**
 * 任务处理器基类
 * 
 * <AUTHOR>
 */
@Slf4j
public abstract class BaseJobProcessor implements BasicProcessor {

    @Override
    public ProcessResult process(TaskContext context) throws Exception {
        long startTime = System.currentTimeMillis();
        String jobName = getJobName();
        
        log.info("开始执行任务: {}, 实例ID: {}, 任务参数: {}", 
                jobName, context.getInstanceId(), context.getJobParams());
        
        try {
            // 执行具体的任务逻辑
            ProcessResult result = doProcess(context);
            
            long endTime = System.currentTimeMillis();
            log.info("任务执行完成: {}, 实例ID: {}, 耗时: {}ms, 结果: {}", 
                    jobName, context.getInstanceId(), (endTime - startTime), result.isSuccess());
            
            return result;
            
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("任务执行失败: {}, 实例ID: {}, 耗时: {}ms, 错误: {}", 
                    jobName, context.getInstanceId(), (endTime - startTime), e.getMessage(), e);
            
            return new ProcessResult(false, "任务执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行具体的任务逻辑
     */
    protected abstract ProcessResult doProcess(TaskContext context) throws Exception;

    /**
     * 获取任务名称
     */
    protected abstract String getJobName();

    /**
     * 创建成功结果
     */
    protected ProcessResult success() {
        return new ProcessResult(true, "任务执行成功");
    }

    /**
     * 创建成功结果（带消息）
     */
    protected ProcessResult success(String message) {
        return new ProcessResult(true, message);
    }

    /**
     * 创建失败结果
     */
    protected ProcessResult failure(String message) {
        return new ProcessResult(false, message);
    }

    /**
     * 解析任务参数
     */
    protected <T> T parseJobParams(TaskContext context, Class<T> clazz) {
        try {
            String jobParams = context.getJobParams();
            if (jobParams == null || jobParams.trim().isEmpty()) {
                return null;
            }
            
            // 这里可以使用JSON工具类解析参数
            // return JsonUtils.fromJson(jobParams, clazz);
            return null;
        } catch (Exception e) {
            log.error("解析任务参数失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 记录任务进度
     */
    protected void reportProgress(TaskContext context, String progress) {
        log.info("任务进度更新: {}, 实例ID: {}, 进度: {}", 
                getJobName(), context.getInstanceId(), progress);
        // 这里可以通过PowerJob的API上报进度
    }
}
