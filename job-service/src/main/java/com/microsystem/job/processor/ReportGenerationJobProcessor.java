package com.microsystem.job.processor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;

/**
 * 报表生成任务处理器
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class ReportGenerationJobProcessor extends BaseJobProcessor {

    @Override
    protected ProcessResult doProcess(TaskContext context) throws Exception {
        log.info("开始执行报表生成任务");
        
        String jobParams = context.getJobParams();
        log.info("任务参数: {}", jobParams);
        
        // 1. 解析报表参数
        reportProgress(context, "正在解析报表参数...");
        Thread.sleep(500);
        
        // 2. 查询数据
        reportProgress(context, "正在查询报表数据...");
        Thread.sleep(2000);
        
        // 3. 生成报表
        reportProgress(context, "正在生成报表文件...");
        Thread.sleep(3000);
        
        // 4. 上传报表文件
        reportProgress(context, "正在上传报表文件...");
        Thread.sleep(1000);
        
        // 5. 发送通知
        reportProgress(context, "正在发送完成通知...");
        Thread.sleep(500);
        
        log.info("报表生成任务执行完成");
        return success("报表生成完成，文件路径: /reports/daily_report_20241203.xlsx");
    }

    @Override
    protected String getJobName() {
        return "报表生成任务";
    }
}
